import React from 'react';
import { View, Text, StyleSheet, Image, FlatList, ImageBackground, TouchableOpacity, ScrollView } from 'react-native';
import { dbEnquiryTab, dbUserFeatureTab, dbMechanicList } from '../../utils/templates/TemplateConfig';
import { AppCommonIcons } from '../../utils/constants/AppStrings';
import { Colors } from '../../utils/constants/Theme';
import { wp } from '../../utils/ResponsiveParams';
import MechanicCard from '../../components/cardstyles/MechanicCard';

interface MechanicItem {
  id: number;
  name: string;
  address: string;
  ratingOutOf: number;
  userRating: number;
  availability: string;
  favourite: boolean;
}

const DashBoard = () => (

  <ImageBackground style={styles.container} source={AppCommonIcons.MCX_BACKGROUND_IMAGE} resizeMode="cover">
    <ScrollView
      style={styles.scrollContainer}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.scrollContent}
      nestedScrollEnabled={true}
    >
      {/* Main Content Container with common padding */}
      <View style={styles.mainContentContainer}>
        {/* Enquiry Tabs */}
        <View style={styles.enquirySection}>
          {dbEnquiryTab.map(item => (
            <TouchableOpacity
              key={item.key}
              style={styles.enquiryFeatureBox}
              activeOpacity={0.7}
            >
              <Text style={styles.enquiryCount}>{item.count}</Text>
              <View style={styles.enquiryTextContainer}>
                <Text style={styles.enquiryLabel}>{item.label}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* User Feature Tabs */}
        <View style={styles.userFeatureSection}>
          {dbUserFeatureTab.map(item => (
            <TouchableOpacity
              key={item.key}
              style={styles.userFeatureBox}
              activeOpacity={0.7}
            >
              <Image source={item.icon} style={styles.featureIcon} />
              <Text style={styles.featureLabel} numberOfLines={2}>{item.label}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Mechanic List */}
        <FlatList
          data={dbMechanicList}
          keyExtractor={(item: MechanicItem) => item.id.toString()}
          renderItem={({ item }: { item: MechanicItem }) => (
            <MechanicCard
              id={item.id}
              name={item.name}
              address={item.address}
              userRating={item.userRating}
              ratingOutOf={item.ratingOutOf}
              availability={item.availability}
              showFavoriteIcon={false}
              cardStyle={styles.mechanicCard}
            />
          )}
          style={styles.mechanicList}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </ScrollView>
  </ImageBackground>
);
const isSmallDevice = wp(100) < 360;
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  // Main container with common horizontal padding
  mainContentContainer: {
    flex: 1,
    paddingHorizontal: 12,
    paddingTop: 10,
    paddingBottom: 20,
  },
  // Enquiry Section Styles
  enquirySection: {
    flexDirection: 'row',
    backgroundColor: Colors.SECONDARY,
    borderRadius: 4,
    alignContent: 'center',
    padding: 6,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_DARK,
  },
  enquiryFeatureBox: {
    flex: 1,
    backgroundColor: Colors.PRIMARY,
    borderRadius: 2,
    minHeight: isSmallDevice ? 55 : 65,
    borderWidth: 1,
    borderColor: Colors.COMMON_WHITE_SHADE,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: isSmallDevice ? 6 : 8,
    paddingVertical: isSmallDevice ? 6 : 8,
  },
  enquiryCount: {
    color: '#fff',
    fontSize: isSmallDevice ? wp(7) : wp(8),
    fontWeight: 'bold',
    minWidth: isSmallDevice ? wp(7) : wp(8),
    textAlign: 'center',
  },
  enquiryTextContainer: {
    flex: 1,
    marginLeft: isSmallDevice ? 6 : 8,
    justifyContent: 'center',
  },
  enquiryLabel: {
    color: '#fff',
    fontSize: isSmallDevice ? wp(2.8) : wp(3),
    fontWeight: '600',
    textAlign: 'left',
    flexWrap: 'wrap',
    lineHeight: isSmallDevice ? wp(3.5) : wp(4),
  },

  // User Feature Section Styles
  userFeatureSection: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: Colors.SECONDARY,
    borderRadius: 4,
    padding: 6,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_DARK,
  },
  userFeatureBox: {
    width: '50%',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 2,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: isSmallDevice ? wp(20) : wp(22),
    borderWidth: 1,
    borderColor: Colors.SECONDARY,
    paddingVertical: isSmallDevice ? wp(2.5) : wp(3),
    paddingHorizontal: isSmallDevice ? wp(1.5) : wp(2),
  },
  featureIcon: {
    width: isSmallDevice ? wp(9) : wp(10),
    height: isSmallDevice ? wp(9) : wp(10),
    marginBottom: isSmallDevice ? wp(1.5) : wp(2),
    tintColor: '#fff',
    resizeMode: 'contain',
  },
  featureLabel: {
    color: '#fff',
    fontSize: isSmallDevice ? wp(2.8) : wp(3),
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: isSmallDevice ? wp(3.5) : wp(3.8),
  },
  mechanicList: {
    marginTop: 2,
  },
  mechanicCard: {
    backgroundColor: '#fff',
    borderRadius: 2,
    padding: 16,
    marginBottom: 2,
  },

});

export default DashBoard;