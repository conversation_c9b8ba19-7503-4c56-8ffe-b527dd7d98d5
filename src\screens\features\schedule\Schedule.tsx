import React, { useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View, Platform, Modal, Image, TextInput } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors, CommonUIParams, Fonts, Sizes, CardStyleConstants } from '../../../utils/constants/Theme';

import { AppCommonIcons, ExceptionStrings, AppStrings } from '../../../utils/constants/AppStrings';
import { VEHICLE_OPTIONS, SERVICE_TYPE_OPTIONS } from '../../../utils/templates/DropdownOptions';
import { alertConfirmationOptions, ssScreeningQuestions, userLocationData } from '../../../utils/templates/TemplateConfig';
import TitleSection from '../../../components/common/TitleSection';
import CustomCardListStyle from '../../../components/cardstyles/CustomCardListStyle';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import CommonDropdown from '../../../components/common/CommonDropdown';
import QuestionRenderer from '../../../components/common/QuestionRenderer';
import CommonCardStyle from '../../../components/common/CommonCardStyle';
import CustomButton from '../../../components/common/CustomButton';
import { ssPriceModelData } from '../../../utils/templates/TemplateConfig';

// Transform data for dropdowns
const vehicleDropdownData = VEHICLE_OPTIONS.map(item => ({ label: item, value: item }));
const serviceTypeDropdownData = SERVICE_TYPE_OPTIONS.map(item => ({ label: item, value: item }));

const Schedule = () => {
  // State management
  const [selectedVehicle, setSelectedVehicle] = useState<string | null>(null);
  const [selectedServiceType, setSelectedServiceType] = useState<string | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<number | null>(null);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [selectedPriceModel, setSelectedPriceModel] = useState<number | null>(null);
  const [selectedAlertConfirmation, setSelectedAlertConfirmation] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);
  const [datePickerValue, setDatePickerValue] = useState<Date>(new Date());
  const [notesText, setNotesText] = useState<string>('');

  // Data availability checks
  const hasVehicleData = vehicleDropdownData && vehicleDropdownData.length > 0;
  const hasLocationData = userLocationData && userLocationData.length > 0;
  const hasServiceData = serviceTypeDropdownData && serviceTypeDropdownData.length > 0;
  const hasScreeningQuestions = ssScreeningQuestions && ssScreeningQuestions.length > 0;
  const hasPriceModelData = ssPriceModelData && ssPriceModelData.length > 0;

  const alertConfirmationData = alertConfirmationOptions.map(item => ({
    label: item,
    value: item
  }));

  // Event handlers
  const handleLocationSelect = (locationId: string | undefined) => {
    if (locationId) {
      setSelectedLocation(Number(locationId));
    }
  };
  const handlePriceModelSelect = (priceModelId: string | undefined) => {
    if (priceModelId) {
      setSelectedPriceModel(Number(priceModelId));
    }
  };
  const handleAnswerChange = (questionId: string, answer: string) => {
    setAnswers(prev => ({ ...prev, [questionId]: answer }));
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }

    if (selectedDate) {
      setDatePickerValue(selectedDate);
      // Format date as MM/DD/YY
      const month = (selectedDate.getMonth() + 1).toString().padStart(2, '0');
      const day = selectedDate.getDate().toString().padStart(2, '0');
      const year = selectedDate.getFullYear().toString().slice(-2);
      const formattedDate = `${month}/${day}/${year}`;
      setSelectedDate(formattedDate);

      if (Platform.OS === 'ios') {
        setShowDatePicker(false);
      }
    } else if (Platform.OS === 'android') {
      // User cancelled the picker on Android
      setShowDatePicker(false);
    }
  };

  return (
    <ScreenLayout
      useScrollView={true}
      useImageBackground={true}
      centerContent={true}
    >
      {/* Header Section */}
      <TitleSection
        title={AppStrings.MCX_SCHEDULE_SERVICE_TITLE}
        bgColor={Colors.PRIMARY}
        textColor="#fff"
      />

      {/* My Vehicle Section */}
      <CommonCardStyle
        header={AppStrings.MCX_MY_VEHICLE_TEXT}
        headerColor={Colors.SECONDARY}
        textColor={Colors.COMMON_GREY_SHADE_DARK}
        cardBackgroundColor="white"
        headerTextSize={CardStyleConstants.HEADER_TEXT_SIZE}
        showException={!hasVehicleData}
        exceptionText={ExceptionStrings.MCX_EXCEPTION_NO_VEHICLE_FOUND_TEXT}
        exceptionIcon={AppCommonIcons.MCX_EXCEPTION_NO_DATA_FOUND_ICON}
      >
        <View style={styles.sectionContainer}>
          <Text style={styles.label}>{AppStrings.MCX_MY_VEHICLE_TEXT}</Text>
          <CommonDropdown
            data={vehicleDropdownData}
            value={selectedVehicle}
            onValueChange={setSelectedVehicle}
            placeholder={AppStrings.MCX_VEHICLE_SELECT_TEXT}
            testID="vehicle-dropdown"
            fontWeight="800"
          />
        </View>
      </CommonCardStyle>

      {/* My Location Section */}
      <CommonCardStyle
        header={AppStrings.MCX_MY_LOCATION_TEXT}
        headerColor={Colors.SECONDARY}
        textColor={Colors.COMMON_GREY_SHADE_DARK}
        cardBackgroundColor="white"
        headerTextSize={CardStyleConstants.HEADER_TEXT_SIZE}
        showException={!hasLocationData}
        exceptionText={ExceptionStrings.MCX_EXCEPTION_NO_LOCATION_FOUND_TEXT}
        exceptionIcon={AppCommonIcons.MCX_APP_ICON}
      >
        {hasLocationData && (
          <View style={styles.sectionContainer}>
            {userLocationData.map((location, index) => (
              <CustomCardListStyle
                key={location.id}
                id={location.id.toString()}
                title={location.title}
                isSelected={selectedLocation === location.id}
                onPress={handleLocationSelect}
                isDefault={location.isDefault}
                rightElementType="radio"
                showHorizontalLine={index < userLocationData.length - 1}
                horizontalLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
                textColor={Colors.COMMON_GREY_SHADE_DARK}
                subtitleColor={Colors.COMMON_GREY_SHADE_DARK}
                indicatorColor={Colors.COMMON_GREY_SHADE_DARK}
                radioButtonColor={Colors.COMMON_GREY_SHADE_DARK}
              />
            ))}
          </View>
        )}
      </CommonCardStyle>

      {/* Service Needed Section */}
      <CommonCardStyle
        header={AppStrings.MCX_SERVICES_NEEDED_TEXT}
        headerColor={Colors.PRIMARY}
        textColor={Colors.COMMON_WHITE_SHADE}
        cardBackgroundColor="white"
        showException={!hasServiceData}
        exceptionText={ExceptionStrings.MCX_EXCEPTION_NO_SERVICE_FOUND_TEXT}
        exceptionIcon={AppCommonIcons.MCX_APP_ICON}
        headerBottomPadding={true}
        headerTextSize={CardStyleConstants.HEADER_TEXT_SIZE_LARGE}
      >
        {hasServiceData && (
          <View style={styles.sectionContainer}>
            <Text style={styles.label}>{AppStrings.MCX_SERVICE_TYPE_KEYWORD}</Text>
            <CommonDropdown
              data={serviceTypeDropdownData}
              value={selectedServiceType}
              onValueChange={setSelectedServiceType}
              placeholder={AppStrings.MCX_SERVICE_TYPE_TEXT}
              testID="service-type-dropdown"
              fontWeight="800"
            />
          </View>
        )}
      </CommonCardStyle>

      {/* Pre Screening Questions */}
      <CommonCardStyle
        header={AppStrings.MCX_PRE_SCREENING_QUESTIONS}
        headerColor={Colors.SECONDARY}
        textColor={Colors.COMMON_GREY_SHADE_DARK}
        cardBackgroundColor="white"
        headerTextSize={CardStyleConstants.HEADER_TEXT_SIZE}
        showException={!hasScreeningQuestions}
        exceptionText={ExceptionStrings.MCX_EXCEPTION_NO_SCREENING_QUESTIONS_TEXT}
        exceptionIcon={AppCommonIcons.MCX_APP_ICON}
      >
        {hasScreeningQuestions && ssScreeningQuestions.map(question => (
          <QuestionRenderer
            key={question.id}
            question={question}
            answer={answers[question.id] || null}
            onAnswerChange={handleAnswerChange}
            questionSpacing={6}
          />
        ))}
      </CommonCardStyle>
      {/* Add Your Own Price Model Section */}
      <CommonCardStyle
        header={AppStrings.MCX_ADD_YOUR_OWN_PRICE_MODEL_TEXT}
        headerColor={Colors.SECONDARY}
        textColor={Colors.COMMON_GREY_SHADE_DARK}
        cardBackgroundColor="white"
        headerTextSize={CardStyleConstants.HEADER_TEXT_SIZE}
        showException={!hasPriceModelData}
        exceptionText={ExceptionStrings.MCX_EXCEPTION_NO_DATA_FOUND_TEXT}
        exceptionIcon={AppCommonIcons.MCX_EXCEPTION_NO_DATA_FOUND_ICON}
      >
        {hasPriceModelData && ssPriceModelData.map((priceModel, index) => (
          <CustomCardListStyle
            key={priceModel.id}
            id={priceModel.id.toString()}
            title={priceModel.title}
            subtitle={priceModel.description}
            rightText={priceModel.price}
            rightTextColor={Colors.PRIMARY}
            rightTextSize={Sizes.MEDIUM}
            isSelected={selectedPriceModel === priceModel.id}
            onPress={handlePriceModelSelect}
            rightElementType="radio"
            showHorizontalLine={index < priceModelData.length - 1}
            horizontalLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
            textColor={Colors.COMMON_GREY_SHADE_DARK}
            subtitleColor={Colors.COMMON_GREY_SHADE_LIGHT}
            radioButtonColor={Colors.COMMON_GREY_SHADE_DARK}
          />
        ))}
      </CommonCardStyle>
      {/* Select Time Section */}
      <CommonCardStyle
        header={AppStrings.MCX_SELECT_TIME_TEXT}
        headerColor={Colors.SECONDARY}
        textColor={Colors.COMMON_GREY_SHADE_DARK}
        cardBackgroundColor="white"
        headerTextSize={CardStyleConstants.HEADER_TEXT_SIZE}
      >
        <View style={styles.sectionContainer}>
          {/* Alerts and Confirmation */}
          <Text style={styles.subSectionLabel}>
            {AppStrings.MCX_ALERTS_AND_CONFIRMATION_TEXT}
          </Text>
          <CommonDropdown
            data={alertConfirmationData}
            value={selectedAlertConfirmation}
            onValueChange={setSelectedAlertConfirmation}
            placeholder={AppStrings.MCX_SELECT_ALERT_CONFIRMATION_TEXT}
            testID="alert-confirmation-dropdown"
            fontWeight="800"
          />

          {/* Date Selection */}
          <Text style={styles.subSectionLabel}>
            {AppStrings.MCX_DATE_TEXT}
          </Text>
          <TouchableOpacity
            style={styles.dateInput}
            onPress={() => setShowDatePicker(true)}
          >
            <Text style={[
              styles.dateInputText,
              !selectedDate && styles.dateInputPlaceholder
            ]}>
              {selectedDate || AppStrings.MCX_DATE_PLACEHOLDER_TEXT}
            </Text>
          </TouchableOpacity>

          {/* Date Picker Modal */}

        </View>
      </CommonCardStyle>
      {Platform.OS === 'ios' ? (
        showDatePicker && (
          <DateTimePicker
            value={datePickerValue}
            mode="date"
            display="default"
            onChange={handleDateChange}
            style={{ width: 150, height: 200 }}
          />
        )
      ) : (
        <Modal
          transparent={true}
          animationType="slide"
          visible={showDatePicker}
          onRequestClose={() => setShowDatePicker(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <DateTimePicker
                value={datePickerValue}
                mode="date"
                display="default"
                onChange={handleDateChange}
              />
            </View>
          </View>
        </Modal>
      )}

      {/* Notes and Images Section */}
      <View style={styles.notesAndImagesSection}>
        <Text style={styles.notesAndImagesHeader}>{AppStrings.MCX_NOTES_AND_IMAGES}</Text>

        {/* Notes Text Area */}
        <TextInput
          style={styles.notesContainer}
          placeholder={AppStrings.MCX_NOTES_PLACEHOLDER_TEXT}
          placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
          value={notesText}
          onChangeText={setNotesText}
          multiline={true}
          textAlignVertical="top"
        />

        {/* Image Upload Icon */}
        <TouchableOpacity style={styles.imageUploadContainer}>
          <View style={styles.imageUploadIcon}>
            <Image
              source={AppCommonIcons.MCX_IMAGE_UPLOAD_ICON}
              style={styles.imageUploadImage}
              resizeMode="contain"
            />
          </View>
        </TouchableOpacity>
      </View>

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <CustomButton
          text="CANCEL SCHEDULE"
          onPress={() => console.log('Cancel Schedule pressed')}
          variant="outline"
          size="small"
          fullWidth={true}
          borderColor={Colors.COMMON_GREY_SHADE_DARK}
          textColor={Colors.COMMON_GREY_SHADE_DARK}
          backgroundColor={Colors.COMMON_BlACK_SHADE}
          style={styles.cancelButton}
          isBoldText={true}
        />

        <CustomButton
          text="SCHEDULE NOW"
          onPress={() => console.log('Schedule Now pressed')}
          variant="primary"
          size="large"
          fullWidth={true}
          backgroundColor={Colors.COMMON_GREY_SHADE_DARK}
          textColor="#fff"
          isBoldText={true}
          style={styles.scheduleButton}
        />
      </View>
    </ScreenLayout>
  );
};

const styles = StyleSheet.create({
  sectionContainer: {
    flex: 1,
  },
  label: {
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: Sizes.MEDIUM,
    color: Colors.COMMON_HEADING_COLOR_1,
    paddingLeft: CardStyleConstants.CONTENT_HORIZONTAL_PADDING,
    paddingTop: CardStyleConstants.CONTENT_TOP_PADDING,
  },
  subSectionLabel: {
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: Sizes.SMALL,
    color: Colors.COMMON_GREY_SHADE_LIGHT,
    paddingLeft: CardStyleConstants.CONTENT_HORIZONTAL_PADDING,
    paddingTop: CardStyleConstants.SUB_SECTION_SPACING,
    marginBottom: CardStyleConstants.SUB_SECTION_LABEL_SPACING,
    letterSpacing: 0.5,
  },
  dateInput: {
    borderWidth: 1,
    borderColor: Colors.DROPDOWN_BORDER_COLOR,
    borderRadius: 4,
    padding: 8,
    marginLeft: CommonUIParams.CUSTOM_PADDING_16,
    marginRight: CommonUIParams.CUSTOM_PADDING_16,
    backgroundColor: '#fff',
    justifyContent: 'center',
    minHeight: 40,
    marginBottom:12,
  },
  dateInputText: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontWeight: '800',
  },
  dateInputPlaceholder: {
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontWeight: '800',
    fontSize: Sizes.MEDIUM,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  notesContainer: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 4,
    minHeight: 80,
    marginLeft: CommonUIParams.CUSTOM_PADDING_16,
    marginRight: CommonUIParams.CUSTOM_PADDING_16,
    marginBottom: 16,
    paddingLeft:4,
    backgroundColor: '#fff',
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.SECONDARY,
    fontStyle: 'italic',
  },
  imageUploadContainer: {
    marginLeft: CommonUIParams.CUSTOM_PADDING_16, 
  },
  imageUploadIcon: {
    width: 28,
    height: 28,
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 2,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
  },
  imageUploadImage: {
    width: 24,
    height: 24,
    tintColor: Colors.COMMON_GREY_SHADE_LIGHT,
  },
  buttonContainer: {
    width: '95%',
    alignSelf: 'center',
    paddingVertical: 16,
    gap: 12,
  },
  cancelButton: {
    marginBottom: 8,
  },
  scheduleButton: {
    marginBottom: 20,
  },
  notesAndImagesSection: {
    backgroundColor: 'white',
    marginVertical: 4,
    paddingVertical: 12,
    width: '95%',
    alignSelf: 'center',
    borderRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
  },
  notesAndImagesHeader: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_MEDIUM,
    color: Colors.COMMON_GREY_SHADE_LIGHT,
    paddingLeft: CommonUIParams.CUSTOM_PADDING_16,
    fontWeight: '600',
  },
});

export default Schedule;
