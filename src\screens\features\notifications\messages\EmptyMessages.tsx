import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { AppCommonIcons, ExceptionStrings } from '../../../../utils/constants/AppStrings';
import { Colors, Sizes } from '../../../../utils/constants/Theme';

interface EmptyMessagesProps {
  message?: string;
}

const EmptyMessages: React.FC<EmptyMessagesProps> = ({ 
  message = ExceptionStrings.MCX_EXCEPTION_NO_MESSAGE_DISPLAY
}) => {
  return (
    <View style={styles.container}>
      <Image 
        source={AppCommonIcons.MCX_NO_MESSAGES_ICON} 
        style={styles.icon} 
      />
      <Text style={styles.text}>{message}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    width: 120,
    height: 120,
  },
  text: {
    fontSize: Sizes.LARGE,
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    textAlign: 'center',
    fontWeight:'500',
  },
});

export default EmptyMessages;