import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors, CommonUIParams, Fonts, Sizes } from '../../utils/constants/Theme';
import { AppStrings } from '../../utils/constants/AppStrings';
import CommonDropdown from './CommonDropdown';
import CommonRadioGroup from './CommonRadioGroup';
import CommonTextInput from './CommonTextInput';

interface Question {
    id: string;
    question: string;
    type: 'dropdown' | 'radio' | 'text';
    options: string[];
}

interface QuestionRendererProps {
    question: Question;
    answer: string | null;
    onAnswerChange: (questionId: string, answer: string) => void;
    containerStyle?: object;
    questionSpacing?: number;
}

const QuestionRenderer = ({
    question,
    answer,
    onAnswerChange,
    containerStyle,
    questionSpacing = 6,
}: QuestionRendererProps) => {
    const handleAnswerChange = (value: string) => {
        onAnswerChange(question.id, value);
    };

    const renderQuestionInput = () => {
        switch (question.type) {
            case 'dropdown':
                const dropdownData = question.options.map(opt => ({ 
                    label: opt, 
                    value: opt 
                }));
                return (
                    <CommonDropdown
                        data={dropdownData}
                        value={answer}
                        onValueChange={handleAnswerChange}
                        placeholder={AppStrings.MCX_DROPDOWN_PLACEHOLDER}
                        testID={question.id}
                        fontWeight="800"
                    />
                );

            case 'radio':
                return (
                    <CommonRadioGroup
                        options={question.options}
                        selectedValue={answer}
                        onValueChange={handleAnswerChange}
                    />
                );

            case 'text':
                return (
                    <CommonTextInput
                        value={answer || ''}
                        onChangeText={handleAnswerChange}
                        placeholder={AppStrings.MCX_TEXT_INPUT_PLACEHOLDER_TYPE_YOUR_ANSWER}
                        placeholderTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
                    />
                );

            default:
                return null;
        }
    };

    return (
        <View style={[
            { marginBottom: questionSpacing },
            containerStyle,
        ]}>
            <Text style={styles.questionLabel}>{question.question}</Text>
            {renderQuestionInput()}
        </View>
    );
};

const styles = StyleSheet.create({
    questionLabel: {
        fontFamily: Fonts.ROBO_REGULAR,
        fontSize: Sizes.MEDIUM,
        color: Colors.SECONDARY,
        paddingLeft: CommonUIParams.CUSTOM_PADDING_16,
        paddingTop: CommonUIParams.CUSTOM_PADDING_14,
    },
});

export default QuestionRenderer;
