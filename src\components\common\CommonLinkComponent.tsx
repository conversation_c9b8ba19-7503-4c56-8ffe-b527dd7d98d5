import React from 'react';
import { Text, StyleSheet, Linking, GestureResponderEvent, TextStyle } from 'react-native';

interface CommonLinkProps {
  url: string;
  children: React.ReactNode;
  style?: TextStyle;
  onPress?: (event: GestureResponderEvent) => void;
}

const CommonLink: React.FC<CommonLinkProps> = ({ url, children, style, onPress }) => {
  const handlePress = (event: GestureResponderEvent) => {
    if (onPress) {
      onPress(event);
    } else {
      Linking.openURL(url);
    }
  };

  return (
    <Text style={[styles.link, style]} onPress={handlePress}>
      {children}
    </Text>
  );
};

const styles = StyleSheet.create({
  link: {
    color: '#a10000', // or use your Colors.linkTextColor
    textDecorationLine: 'underline',
    fontWeight: 'bold',
  },
});

export default CommonLink;
