import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image, ViewStyle, TextStyle, ImageStyle } from 'react-native';
import { Colors, Fonts, Sizes } from '../../utils/constants/Theme';
import HorizontalDivider from './HorizontalDivider';
import { AppCommonIcons } from '../../utils/constants/AppStrings';

interface CustomHelpListItemProps {
  title: string;
  titleColor?: string;
  titleSize?: number;
  onPress?: () => void;
  horizontalDivider?: boolean;
  horizontalLineColor?: string;
  horizontalLineSize?: number;
  containerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  iconStyle?: ImageStyle;
  backgroundColor?: string;
}

const CustomHelpListItem: React.FC<CustomHelpListItemProps> = ({
  title,
  titleColor = Colors.COMMON_COMPONENT_TEXT_COLOR,
  titleSize = Sizes.MEDIUM,
  onPress,
  horizontalDivider = false,
  horizontalLineColor = Colors.COMMON_GREY_SHADE_LIGHT,
  horizontalLineSize = 1,
  containerStyle,
  titleStyle,
  iconStyle,
  backgroundColor = Colors.COMMON_WHITE_SHADE,
}) => {
  return (
    <View style={[styles.container, { backgroundColor }, containerStyle, !horizontalDivider && styles.gap]}>
      <TouchableOpacity style={styles.touchable} onPress={onPress} activeOpacity={0.7}>
        <Text style={[styles.title, { color: titleColor, fontSize: titleSize }, titleStyle]} numberOfLines={0}>
          {title}
        </Text>
        <Image source={AppCommonIcons.MCX_ARROW_RIGHT} style={[styles.icon, iconStyle]} />
      </TouchableOpacity>
      {horizontalDivider && (
        <HorizontalDivider color={horizontalLineColor} height={horizontalLineSize} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  gap: {
    backgroundColor: 'transparent',
    marginBottom: 1,
  },
  touchable: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 12,
    justifyContent: 'space-between',
  },
  title: {
    fontFamily: Fonts.ROBO_REGULAR,
    flex: 1,
    fontWeight:'700',
  },
  icon: {
    width: 20,
    height: 20,
    tintColor: Colors.PRIMARY, // red color as in screenshot
    marginLeft: 12,
  },
});

export default CustomHelpListItem;
