import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, FlatList, Modal, Animated } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Colors, CommonUIParams, Fonts, Sizes } from '../../utils/constants/Theme';

interface DropdownOption {
    label: string;
    value: string;
}

interface CommonDropdownProps {
    data: DropdownOption[];
    value: string | null;
    onValueChange: (value: string) => void;
    placeholder: string;
    style?: object;
    testID?: string;
    disabled?: boolean;
    placeholderTextColor?: string;
    selectedTextColor?: string;
    fontWeight?: 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900';
}

const CommonDropdown = ({
    data,
    value,
    onValueChange,
    placeholder,
    style,
    testID,
    disabled = false,
    placeholderTextColor,
    selectedTextColor,
    fontWeight = 'normal',
}: CommonDropdownProps) => {
    const [modalVisible, setModalVisible] = useState(false);
    const slideAnim = useRef(new Animated.Value(300)).current;

    const selectedItem = data.find(item => item.value === value);
    const displayText = selectedItem ? selectedItem.label : placeholder;
    const displayTitle = placeholder;

    const openModal = () => {
        if (!disabled) {
            setModalVisible(true);
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            }).start();
        }
    };

    const closeModal = () => {
        Animated.timing(slideAnim, {
            toValue: 300,
            duration: 300,
            useNativeDriver: true,
        }).start(() => {
            setModalVisible(false);
        });
    };

    const handleSelect = (selectedValue: string) => {
        onValueChange(selectedValue);
        closeModal();
    };



    return (
        <View style={[styles.container, style]}>
            <TouchableOpacity
                style={[styles.dropdownButton, disabled && styles.dropdownDisabled]}
                onPress={openModal}
                disabled={disabled}
                testID={testID}
            >
                <Text style={[styles.dropdownButtonText, {
                    color: selectedItem ? (selectedTextColor || Colors.COMMON_DROP_DOWN_TEXT_COLOR) : (placeholderTextColor || Colors.COMMON_DROP_DOWN_TEXT_COLOR),
                    fontWeight: fontWeight,
                }]}>
                    {displayText}
                </Text>
                <Icon name="arrow-drop-down" size={24} color="#666666" />
            </TouchableOpacity>
            <Modal
                visible={modalVisible}
                transparent
                animationType="none"
                onRequestClose={closeModal}
            >
                <View style={styles.modalContainer}>
                    <TouchableOpacity
                        style={styles.modalOverlay}
                        activeOpacity={1}
                        onPress={closeModal}
                    />

                    <Animated.View
                        style={[
                            styles.bottomSheet,
                            { transform: [{ translateY: slideAnim }] },
                        ]}
                    >
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalHeaderText}>{displayTitle}</Text>
                        </View>

                        <FlatList
                            data={data}
                            keyExtractor={(item) => item.value}
                            style={styles.flatListStyle}
                            renderItem={({ item }) => (
                                <TouchableOpacity
                                    style={styles.optionItem}
                                    onPress={() => handleSelect(item.value)}
                                >
                                    <Text style={styles.optionText}>{item.label}</Text>
                                </TouchableOpacity>
                            )}
                            ListFooterComponent={
                                <TouchableOpacity
                                    style={styles.cancelButton}
                                    onPress={closeModal}
                                >
                                    <Text style={styles.cancelButtonText}>Cancel</Text>
                                </TouchableOpacity>
                            }
                        />
                    </Animated.View>
                </View>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        marginLeft: CommonUIParams.CUSTOM_PADDING_16,
        marginRight: CommonUIParams.CUSTOM_PADDING_16,
        marginBottom: 6,
    },
    dropdownButton: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
        borderRadius: 2,
        paddingLeft: 12,
        paddingRight: 1,
        paddingVertical: 8,
        backgroundColor: 'transparent',
    },
    dropdownButtonText: {
        fontSize: Sizes.MEDIUM,
        fontFamily: Fonts.ROBO_REGULAR,
        color: Colors.COMMON_DROP_DOWN_TEXT_COLOR,
        flex: 1,
    },
    dropdownDisabled: {
        backgroundColor: Colors.COMMON_GREY_SHADE_LIGHT,
        opacity: 0.6,
    },

    modalContainer: {
        flex: 1,
        justifyContent: 'flex-end',
        zIndex: 9999,
    },
    modalOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    bottomSheet: {
        backgroundColor: '#FFFFFF',
        maxHeight: '70%',
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        elevation: 5,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: -2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    modalHeader: {
        padding: 8,
    },
    modalHeaderText: {
        fontSize: 18,
        color: Colors.COMMON_GREY_SHADE_DARK,
        fontFamily: Fonts.ROBO_REGULAR,
    },
    optionItem: {
        padding: 12,
    },
    optionText: {
        fontSize: Sizes.MEDIUM,
        fontFamily: Fonts.ROBO_REGULAR,
        color: Colors.COMMON_DROP_DOWN_TEXT_COLOR,
    },
    cancelButton: {
        padding: 16,
        alignItems: 'center',
        marginTop: 8,
        marginBottom: 16,
    },
    cancelButtonText: {
        fontSize: Sizes.MEDIUM,
        fontFamily: Fonts.ROBO_REGULAR,
        color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    },
    flatListStyle: {
        minHeight: 100,
        flexGrow: 0,
    },
});

export default CommonDropdown;
