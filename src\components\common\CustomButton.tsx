import React from 'react';
import { TouchableOpacity, Text, ViewStyle, TextStyle } from 'react-native';
import { Colors, Fonts, Sizes } from '../../utils/constants/Theme';

interface CustomButtonProps {
  text: string;
  onPress: () => void;
  size?: 'small' | 'medium' | 'large';
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  backgroundColor?: string;
  textColor?: string;
  textSize?: number;
  borderColor?: string;
  borderWidth?: number;
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  fullWidth?: boolean;
  isBoldText?: boolean;
  // Bottom button specific props
  isBottomButton?: boolean;
  bottomLineWidth?: number;
  bottomLineColor?: string;
}

const CustomButton: React.FC<CustomButtonProps> = ({
  text,
  onPress,
  size = 'medium',
  variant = 'primary',
  backgroundColor,
  textColor,
  textSize,
  borderColor,
  borderWidth,
  disabled = false,
  style,
  textStyle,
  fullWidth = false,
  isBoldText = false,
  // Bottom button props
  isBottomButton = false,
  bottomLineWidth = 0,
  bottomLineColor,
}) => {
  // Size configurations
  const sizeConfig = {
    small: {
      paddingVertical: 12,
      paddingHorizontal: 20,
      fontSize: Sizes.MEDIUM,
      minHeight: 32,
    },
    medium: {
      paddingVertical: 12,
      paddingHorizontal: 24,
      fontSize: Sizes.MEDIUM,
      minHeight: 40,
    },
    large: {
      paddingVertical: 16,
      paddingHorizontal: 32,
      fontSize: Sizes.LARGE,
      minHeight: 48,
    },
  };

  // Variant configurations
  const variantConfig = {
    primary: {
      backgroundColor: Colors.PRIMARY,
      textColor: Colors.BUTTON_TEXT_COLOR,
      borderColor: Colors.PRIMARY,
      borderWidth: 0,
    },
    secondary: {
      backgroundColor: Colors.SECONDARY,
      textColor: '#fff',
      borderColor: Colors.SECONDARY,
      borderWidth: 0,
    },
    outline: {
      backgroundColor: 'transparent',
      textColor: Colors.PRIMARY,
      borderColor: Colors.PRIMARY,
      borderWidth: 2,
    },
    ghost: {
      backgroundColor: 'transparent',
      textColor: Colors.PRIMARY,
      borderColor: 'transparent',
      borderWidth: 0,
    },
  };

  const currentSize = sizeConfig[size];
  const currentVariant = variantConfig[variant];

  const buttonStyle: ViewStyle = {
    ...currentSize,
    backgroundColor: backgroundColor || currentVariant.backgroundColor,
    alignItems: 'center',
    justifyContent: 'center',
    opacity: disabled ? 0.6 : 1,
    ...(fullWidth && { alignSelf: 'stretch' }),
    // Apply border styling conditionally
    ...(isBottomButton ? {
      // For bottom buttons, only apply top border
      borderTopWidth: bottomLineWidth,
      borderTopColor: bottomLineColor,
      borderRadius: 0,
    } : {
      // For regular buttons, apply normal border
      borderColor: borderColor || currentVariant.borderColor,
      borderWidth: borderWidth !== undefined ? borderWidth : currentVariant.borderWidth,
      borderRadius: 4,
    }),
    ...style,
  };

  const buttonTextStyle: TextStyle = {
    color: textColor || currentVariant.textColor,
    fontSize: textSize || currentSize.fontSize,
    fontFamily: isBoldText ? Fonts.ROBO_BOLD : Fonts.ROBO_MEDIUM,
    fontWeight: isBoldText ? 'bold' : '600',
    textAlign: 'center',
    ...textStyle,
  };

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <Text style={buttonTextStyle}>{text}</Text>
    </TouchableOpacity>
  );
};

export default CustomButton;
