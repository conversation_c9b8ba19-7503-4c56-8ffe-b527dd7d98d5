import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { AppStrings, RouteNames } from '../../utils/constants/AppStrings';
import CommonLink from '../../components/common/CommonLinkComponent';
import { useNavigation } from '@react-navigation/native';
import { Colors, Fonts } from '../../utils/constants/Theme';
import HorizontalDivider from '../../components/common/HorizontalDivider';


const LoginMainScreen = () => {
  const navigation = useNavigation();
  return (
    <View style={styles.container}>
      <Image
        source={require('../../assets/mycanx_logo.png')}
        style={styles.logo}
        resizeMode="contain"
      />

      <TouchableOpacity style={styles.signUpBtn} onPress={() => navigation.navigate(RouteNames.MCX_NAV_ACCOUNT_REGISTRATION as never)}>
        <Text style={styles.signUpText}>{AppStrings.MCX_SIGN_UP}</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.loginBtn} onPress={() => navigation.navigate('AppLoginPageScreen')}>
        <Text style={styles.loginText}>{AppStrings.MCX_LOGIN}</Text>
      </TouchableOpacity>

      <Text style={styles.orText}>{AppStrings.MCX_OR_WITH}</Text>

      <View style={styles.socialRow}>
        <TouchableOpacity style={[styles.socialButton, { backgroundColor: '#3b5998' }]}>
          <Icon name="facebook" size={24} color="#fff" />
        </TouchableOpacity>
        <TouchableOpacity style={[styles.socialButton, { backgroundColor: '#fff' }]}>
          <Image
            source={require('../../assets/social-network-icons/google-icon.png')}
            style={styles.socialIcon}
            resizeMode="contain"
          />
        </TouchableOpacity>
      </View>
      {/* Divider line */}
      <HorizontalDivider isFullWidth={true} />
      <Text style={styles.termsText}>
        {AppStrings.MCX_TERMS_PREFIX}{'\n'}
        <CommonLink url="https://your-privacy-policy-url.com" style={styles.linkText}>
          {AppStrings.MCX_PRIVACY_POLICY}
        </CommonLink> {AppStrings.MCX_AND_KEYWORD} <CommonLink url="https://your-terms-of-service-url.com" style={styles.linkText}>
          {AppStrings.MCX_TERMS_OF_SERVICE}
        </CommonLink>
      </Text>
    </View>
  );
};

export default LoginMainScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.BACKGROUND,
  },
  logo: {
    height: 180,
    width: 180,
    marginBottom: 40,
  },
  signUpBtn: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 10,
    paddingHorizontal: 80,
    borderRadius: 2,
    marginBottom: 10,
    alignItems: 'center',
    alignSelf: 'stretch',
    marginHorizontal: 14,
  },
  signUpText: {
    color: Colors.BUTTON_TEXT_COLOR,
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  loginBtn: {
    borderColor: Colors.BORDER_COLOR,
    borderWidth: 1.2,
    paddingVertical: 8,
    paddingHorizontal: 80,
    borderRadius: 2,
    marginBottom: 30,
    alignItems: 'center',
    alignSelf: 'stretch',
    marginHorizontal: 14,
  },
  loginText: {
    color: '#a10000',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  orText: {
    color: '#999',
    fontSize: 12,
    marginBottom: 10,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  socialRow: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  socialButton: {
    marginHorizontal: 12,
    padding: 10,
  },
  termsText: {
    fontSize: 12,
    color: Colors.TERMS_TEXT_COLOR,
    textAlign: 'center',
    paddingHorizontal: 30,
    fontFamily: Fonts.ROBO_REGULAR,
    lineHeight: 25,
  },
  linkText: {
    color: Colors.LINK_TEXT_COLOR,
    fontWeight: 'bold',
    fontFamily: Fonts.ROBO_BOLD,
  },
  socialIcon: {
    width: 24,
    height: 24,
  },

});
