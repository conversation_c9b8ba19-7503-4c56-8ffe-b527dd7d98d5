import React from 'react';
import { View, StyleSheet } from 'react-native';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import CommonCardStyle from '../../../components/common/CommonCardStyle';
import { Colors, CardStyleConstants } from '../../../utils/constants/Theme';
import { AppStrings, ExceptionStrings, AppCommonIcons } from '../../../utils/constants/AppStrings';
import { upcomingAppointments, otherAppointments } from '../../../utils/templates/TemplateConfig';

const Appointments = () => {
  // Check if data exists
  const hasUpcomingAppointments = upcomingAppointments && upcomingAppointments.length > 0;
  const hasOtherAppointments = otherAppointments && otherAppointments.length > 0;

  return (
    <ScreenLayout
      useScrollView={true}
      useImageBackground={true}
      centerContent={false}
      topPadding={12}
    >
      <View style={styles.container}>
        {/* Upcoming Appointments Section */}
        <CommonCardStyle
          header={AppStrings.MCX_UPCOMING_APPOINTMENTS_TITLE}
          headerColor={Colors.SECONDARY}
          textColor={Colors.COMMON_WHITE_SHADE}
          cardBackgroundColor={Colors.COMMON_WHITE_SHADE}
          headerTextSize={CardStyleConstants.HEADER_TEXT_SIZE}
          showException={!hasUpcomingAppointments}
          exceptionText={ExceptionStrings.MCX_EXCEPTION_NO_UPCOMING_APPOINTMENTS_TEXT}
          exceptionIcon={AppCommonIcons.MCX_EXCEPTION_NO_DATA_FOUND_ICON}
        >
          {hasUpcomingAppointments && (
            <View style={styles.appointmentsContainer}>
              {/* Upcoming appointments content would go here when data exists */}
            </View>
          )}
        </CommonCardStyle>

        {/* Other Appointments Section */}
        <CommonCardStyle
          header={AppStrings.MCX_OTHER_APPOINTMENTS_TITLE}
          headerColor={Colors.SECONDARY}
          textColor={Colors.COMMON_GREY_SHADE_DARK}
          cardBackgroundColor={Colors.COMMON_WHITE_SHADE}
          headerTextSize={CardStyleConstants.HEADER_TEXT_SIZE}
          showException={!hasOtherAppointments}
          exceptionText={ExceptionStrings.MCX_EXCEPTION_NO_OTHER_APPOINTMENTS_TEXT}
          exceptionIcon={AppCommonIcons.MCX_EXCEPTION_NO_DATA_FOUND_ICON}
        >
          {hasOtherAppointments && (
            <View style={styles.appointmentsContainer}>
              {/* Other appointments content would go here when data exists */}
            </View>
          )}
        </CommonCardStyle>
      </View>
    </ScreenLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
  },
  appointmentsContainer: {
    padding: 16,
  },
});

export default Appointments;
