import React from 'react';
import { View, Text, StyleSheet, FlatList, Image } from 'react-native';
import EmptyPending from './EmptyPending';
import { AppStrings } from '../../../../utils/constants/AppStrings';
import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';
import { AppCommonIcons } from '../../../../utils/constants/AppStrings';
import { pendingTasksData, taskStatusData } from '../../../../utils/templates/TemplateConfig';

type TaskStatus = typeof taskStatusData[keyof typeof taskStatusData];

interface PendingTask {
  id: string;
  date: string;
  month: string;
  title: string;
  subtitle: string;
  status: TaskStatus;
  icon: object;
}

const ItemSeparator = () => <View style={styles.itemSeparator} />;

const PendingContent = () => {
  // Get pending tasks data from template config
  const pendingTasks: PendingTask[] = pendingTasksData.map(task => ({
    ...task,
    status: task.status,
    icon: AppCommonIcons.MCX_SETTINGS_ICON,
  }));

  const renderPendingTask = ({ item }: { item: PendingTask }) => {
    return (
      <View style={styles.taskContainer}>
        {/* Left icon block */}
        <View style={styles.leftIconBlock}>
          <Image source={AppCommonIcons.MCX_SETTINGS_ICON} style={styles.leftIcon} />
        </View>

        {/* Date block */}
        <View style={styles.dateCard}>
          <Text style={styles.dateNumber}>{item.date}</Text>
          <Text style={styles.dateMonth}>{item.month}</Text>
        </View>

        {/* Right content block */}
        <View style={styles.taskContent}>
          <View style={styles.taskHeader}>
            <Image source={item.icon} style={styles.taskIcon} />
          </View>
          
          <View style={styles.taskInfo}>
            <View style={styles.taskTextContainer}>
              <Text style={styles.taskTitle}>{item.title}</Text>
              <Text style={styles.taskSubtitle}>{item.subtitle}</Text>
            </View>
            
            <View style={[
              styles.statusContainer,
              item.status === taskStatusData.PENDING ? styles.pendingStatus : styles.declinedStatus,
            ]}>
              <Text style={styles.statusText}>{item.status}</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  const renderContent = () => {
    if (pendingTasks.length === 0) {
      return <EmptyPending message={AppStrings.MCX_PENDING_MESSAGES_TEXT} />;
    }

    return (
      <FlatList
        data={pendingTasks}
        keyExtractor={(item) => item.id}
        renderItem={renderPendingTask}
        style={styles.tasksList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.tasksListContent}
        ItemSeparatorComponent={ItemSeparator}
      />
    );
  };

  return (
    <View style={styles.container}>
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tasksList: {
    marginBottom: 20,
  },
  tasksListContent: {
    paddingTop: 8,
    paddingBottom: 20,
  },
  itemSeparator: {
    height: 8,
  },
  taskContainer: {
    height: 120,
    flexDirection: 'row',
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 2,
    marginHorizontal: 12,
    shadowColor: Colors.COMMON_GREY_SHADOW_LIGHT_COLOR,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  dateCard: {
    width: 60,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dateNumber: {
    fontSize: Sizes.XXXLARGE,
    fontWeight: 'bold',
    color: Colors.COMMON_WHITE_SHADE,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  dateMonth: {
    fontSize: Sizes.SMALL,
    color: Colors.COMMON_WHITE_SHADE,
    fontFamily: Fonts.ROBO_REGULAR,
    opacity: 0.7,
  },
  taskContent: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderTopRightRadius: 2,
    borderBottomRightRadius: 2,
  },
  taskHeader: {
    paddingHorizontal: 12,
    paddingTop: 12,
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
  },
  taskIcon: {
    width: 40,
    height: 40,
    opacity: 0.6,
    tintColor: Colors.COMMON_GREY_SHADE_DARK,
  },
  taskInfo: {
    flex: 1,
    paddingLeft: 12,
    borderLeftWidth: 1,
    borderLeftColor: Colors.COMMON_GREY_SHADOW_LIGHT_COLOR,
    justifyContent: 'space-between',
  },
  taskTextContainer: {
    paddingTop: 12,
  },
  taskTitle: {
    fontSize: Sizes.MEDIUM,
    fontWeight: 'bold',
    color: Colors.COMMON_BlACK_SHADE,
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 2,
  },
  taskSubtitle: {
    fontSize: Sizes.SMALL,
    color: Colors.PRIMARY,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '600',
  },
  statusContainer: {
    alignSelf: 'stretch',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginTop: 4,
  },
  pendingStatus: {
    backgroundColor: '#E53E3E',
  },
  declinedStatus: {
    backgroundColor: '#FF8C00',
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: Sizes.SMALL,
    fontWeight: 'bold',
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
  },
  leftIconBlock: {
    width: 60,
    height: '100%',
    backgroundColor: Colors.PRIMARY_DARK,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 2,
    borderBottomLeftRadius: 2,
  },
  leftIcon: {
    width: 32,
    height: 32,
    opacity: 0.9,
    tintColor: Colors.COMMON_WHITE_SHADE,
  },
});

export default PendingContent;
