import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';

const RegistrationStep2 = () => {
  const [address, setAddress] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [zipCode, setZipCode] = useState('');

  const handleContinue = () => {
    console.log('Step 2 - Continue pressed');
  };

  return (
    <View style={styles.container}>
      <View style={styles.formSection}>
        <RegistrationTitleSection
          title="Address Information"
          backgroundColor="#FFFFFF"
          borderBottomWidth={1}
          borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
          paddingVertical={16}
          paddingHorizontal={0}
        />

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Street Address</Text>
          <CommonTextInput
            value={address}
            onChangeText={setAddress}
            placeholder="Enter your street address"
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>City</Text>
          <CommonTextInput
            value={city}
            onChangeText={setCity}
            placeholder="Enter your city"
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
          />
        </View>

        <View style={styles.rowContainer}>
          <View style={styles.halfInputContainer}>
            <Text style={styles.inputLabel}>State</Text>
            <CommonTextInput
              value={state}
              onChangeText={setState}
              placeholder="State"
              style={styles.textInput}
              placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            />
          </View>

          <View style={styles.halfInputContainer}>
            <Text style={styles.inputLabel}>Zip Code</Text>
            <CommonTextInput
              value={zipCode}
              onChangeText={setZipCode}
              placeholder="Zip Code"
              style={styles.textInput}
              placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
              keyboardType="numeric"
            />
          </View>
        </View>
      </View>

      <View style={styles.bottomContainer}>
        <CustomButton
          text="CONTINUE"
          onPress={handleContinue}
          variant="outline"
          size="large"
          backgroundColor="transparent"
          textColor={Colors.COMMON_GREY_SHADE_LIGHT}
          borderColor={Colors.COMMON_GREY_SHADE_LIGHT}
          borderWidth={1}
          style={styles.continueButton}
          isBoldText={true}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 0,
    justifyContent: 'flex-start',
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 12,
    borderRadius: 2,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    alignSelf: 'stretch',
    minHeight: 'auto',
    overflow: 'hidden',
  },
  inputContainer: {
    marginBottom: 16,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  halfInputContainer: {
    flex: 0.48,
  },
  inputLabel: {
    fontSize: Sizes.MEDIUM,
    fontWeight: '500',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 30,
    left: 24,
    right: 24,
  },
  continueButton: {
    borderRadius: 4,
  },
});

export default RegistrationStep2;
