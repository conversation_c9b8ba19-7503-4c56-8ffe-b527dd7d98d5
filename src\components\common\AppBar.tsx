import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { AppCommonIcons } from '../../utils/constants/AppStrings';
import { Colors, CommonUIParams } from '../../utils/constants/Theme';
import { useNavigation, DrawerActions } from '@react-navigation/native';
import { RouteNames } from '../../utils/constants/AppStrings';

interface AppBarProps {
  onMailPress?: () => void;
  onChatPress?: () => void;
  onMenuPress?: () => void;
  onBackPress?: () => void;
  // Icon visibility props
  showMailIcon?: boolean;
  showChatIcon?: boolean;
  showMenuIcon?: boolean;
  showBackButton?: boolean;
  showLogo?: boolean;
  // Custom title
  title?: string;
  titleColor?: string;
}

const AppBar: React.FC<AppBarProps> = ({
  onMailPress,
  onChatPress,
  onMenuPress,
  onBackPress,
  showMailIcon = true,
  showChatIcon = true,
  showMenuIcon = true,
  showBackButton = false,
  showLogo = true,
  title,
  titleColor = '#fff',
}) => {
  const navigation = useNavigation();
  const handleMenuIconPress = () => {
    navigation.dispatch(DrawerActions.toggleDrawer());
    if (onMenuPress) {
      onMenuPress();
    }
  };

  const handleChatPress = () => {
    navigation.navigate(RouteNames.MCX_NAV_MESSAGES as never);
    if (onChatPress) {
      onChatPress();
    }
  };

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      navigation.goBack();
    }
  };

  return (
    <SafeAreaView style={styles.safeArea} edges={['top']}>
      <View style={styles.container}>
        {/* Left Section */}
        <View style={styles.leftSection}>
          {showBackButton && (
            <TouchableOpacity onPress={handleBackPress} style={styles.iconButton}>
              <Image source={AppCommonIcons.MCX_ARROW_RIGHT} style={[styles.icon, styles.backArrow]} />
            </TouchableOpacity>
          )}
          {showMailIcon && !showBackButton && (
            <TouchableOpacity onPress={onMailPress} style={styles.iconButton}>
              <Image source={AppCommonIcons.MCX_MESSAGE_ICON} style={styles.messageIcon} />
            </TouchableOpacity>
          )}
        </View>

        {/* Center Section */}
        <View style={styles.titleContainer}>
          {showLogo && !title && (
            <Image
              source={AppCommonIcons.MCX_MYCANX_LOGO_TEXT}
              style={styles.logo}
              resizeMode="contain"
            />
          )}
          {title && (
            <Text style={[styles.titleText, { color: titleColor }]}>
              {title}
            </Text>
          )}
        </View>

        {/* Right Section */}
        <View style={styles.rightIcons}>
          {showChatIcon && (
            <TouchableOpacity onPress={handleChatPress} style={styles.iconButton}>
              <Image source={AppCommonIcons.MCX_CHAT_ICON} style={styles.icon} />
            </TouchableOpacity>
          )}
          {showMenuIcon && (
            <TouchableOpacity onPress={handleMenuIconPress} style={styles.iconButton}>
              <Image source={AppCommonIcons.MCX_MENU_ICON} style={styles.icon} />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    backgroundColor: Colors.APPBAR_BG_COLOR,
    zIndex: 10,
  },
  container: {
    height: Platform.OS === 'ios' ? 44 : CommonUIParams.CUSTOM_APP_BAR_TITLE_HEIGHT,
    backgroundColor: Colors.APPBAR_BG_COLOR,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  iconButton: {
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 40,
    minHeight: 40,
  },
  icon: {
    width: 24,
    height: 24,
    tintColor: '#fff',
    resizeMode: 'contain',
  },
  messageIcon: {
    width: 24,
    height: 24,
    tintColor: '#fff',
    resizeMode: 'contain',
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logo: {
    height: 24,
    width: 120,
    alignSelf: 'center',
  },
  rightIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 40,
  },
  titleText: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  backArrow: {
    transform: [{ rotate: '180deg' }],
  },
});

export default AppBar;
