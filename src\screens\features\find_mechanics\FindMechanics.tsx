import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import CommonTextInput from '../../../components/common/CommonTextInput';
import CommonDropdown from '../../../components/common/CommonDropdown';
import MechanicCard from '../../../components/cardstyles/MechanicCard';
import { dbMechanicList, fmServiceTypeOptions, fmAvailabilityOptions } from '../../../utils/templates/TemplateConfig';
import { Colors, Fonts, Sizes } from '../../../utils/constants/Theme';
import { AppStrings } from '../../../utils/constants/AppStrings';


interface MechanicItem {
  id: number;
  name: string;
  address: string;
  userRating: number;
  ratingOutOf: number;
  availability: string;
  serviceTypes?: string[];
  favourite: boolean;
}

const FindMechanic = () => {
  const [searchText, setSearchText] = useState<string>('');
  const [selectedServiceType, setSelectedServiceType] = useState<string | null>(null);
  const [selectedAvailability, setSelectedAvailability] = useState<string | null>(null);
  const [favorites, setFavorites] = useState<Set<number>>(new Set());

  // Use custom dropdown data from TemplateConfig
  const serviceTypeDropdownData = fmServiceTypeOptions;
  const availabilityDropdownData = fmAvailabilityOptions;

  // Filter mechanics based on search and filters
  const filteredMechanics = dbMechanicList.filter(mechanic => {
    const matchesSearch = mechanic.name.toLowerCase().includes(searchText.toLowerCase()) ||
      mechanic.address.toLowerCase().includes(searchText.toLowerCase());

    // Convert availability to match dropdown values
    const mechanicAvailability = mechanic.availability.toLowerCase().replace(' ', '_');
    const matchesAvailability = !selectedAvailability || mechanicAvailability === selectedAvailability;

    // Service type filtering (if mechanic has serviceTypes array, otherwise show all)
    const matchesServiceType = !selectedServiceType ||
      (mechanic.serviceTypes && mechanic.serviceTypes.includes(selectedServiceType));

    return matchesSearch && matchesAvailability && matchesServiceType;
  });

  const toggleFavorite = (mechanicId: number) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(mechanicId)) {
      newFavorites.delete(mechanicId);
    } else {
      newFavorites.add(mechanicId);
    }
    setFavorites(newFavorites);
  };

  const renderMechanicCard = ({ item }: { item: MechanicItem }) => (
    <MechanicCard
      id={item.id}
      name={item.name}
      address={item.address}
      userRating={item.userRating}
      ratingOutOf={item.ratingOutOf}
      availability={item.availability}
      isFavorite={item.favourite}
      onFavoriteToggle={toggleFavorite}
      showFavoriteIcon={item.favourite}
      cardStyle={styles.findMechanicCard}
    />
  );

  return (
    <ScreenLayout
      useScrollView={false}
      useImageBackground={true}
      centerContent={false}
       useHorizontalPadding={true}
    >
      <View style={styles.container}>
        {/* Search and Filter Section with Table Design */}
        <View style={styles.tableContainer}>
          {/* Search Input Row */}
          <View style={styles.searchRow}>
            <CommonTextInput
              value={searchText}
              onChangeText={setSearchText}
              placeholder={AppStrings.MCX_SEARCH_NEARBY_TEXT}
              backgroundColor={Colors.SECONDARY}
              placeholderTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
            />
          </View>

          {/* Filter Dropdowns Row */}
          <View style={styles.filtersRow}>
            <View style={styles.filterCell}>
              <Text style={styles.filterLabel}>{AppStrings.MCX_SERVICE_TYPE_KEYWORD}</Text>
              <CommonDropdown
                data={serviceTypeDropdownData}
                value={selectedServiceType}
                onValueChange={setSelectedServiceType}
                placeholder={AppStrings.MCX_SERVICE_TYPE_TEXT}
                style={styles.dropdownStyle}
                placeholderTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
                selectedTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
                fontWeight="800"
              />
            </View>
            <View style={styles.filterCellLast}>
              <Text style={styles.filterLabel}>{AppStrings.MCX_MECHANIC_AVAILABILITY_TEXT}</Text>
              <CommonDropdown
                data={availabilityDropdownData}
                value={selectedAvailability}
                onValueChange={setSelectedAvailability}
                placeholder={AppStrings.MCX_SELECT_AVAILABILITY_TEXT}
                style={styles.dropdownStyle}
                placeholderTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
                selectedTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
                fontWeight="800"
              />
            </View>
          </View>
        </View>

        {/* Mechanics List */}
        <FlatList
          data={filteredMechanics}
          keyExtractor={(item: MechanicItem) => item.id.toString()}
          renderItem={renderMechanicCard}
          style={styles.mechanicsList}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContent}
        />
      </View>
    </ScreenLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tableContainer: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_DARK,
    marginTop: 16,
  },
  searchRow: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.COMMON_GREY_SHADE_DARK,
    paddingHorizontal: 2,
    paddingVertical: 18,
    paddingRight: 8,
  },
  searchInput: {
    marginLeft: 0,
    marginRight: 0,
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 0,
    marginTop: 0,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '800',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
  },
  filtersRow: {
    flexDirection: 'row',
  },
  filterCell: {
    flex: 1,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  filterCellLast: {
    flex: 1,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  filterLabel: {
    fontSize: Sizes.SMALL,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.COMMON_GREY_SHADE_LIGHT,
    textTransform: 'uppercase',
  },
  dropdownStyle: {
    marginLeft: 0,
    marginRight: 0,
    marginBottom: 0,
    backgroundColor: Colors.SECONDARY,
    borderWidth: 0,
    borderRadius: 0,
  },
  mechanicsList: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 20,
  },
  findMechanicCard: {
    backgroundColor: '#fff',
    borderRadius: 2,
    padding: 16,
    marginBottom: 2,
  },

});

export default FindMechanic;