import { DashboardIcons, AppStrings, AppCommonIcons } from '../../utils/constants/AppStrings';
export const dbEnquiryTab = [
  { key: 'upcoming', label: 'UPCOMING\nAPPOINTMENTS', count: 0 },
  { key: 'pending', label: 'PENDING REQUEST', count: 0 },
];

export const dbUserFeatureTab = [
  { key: 'oil', label: 'Oil Change', icon:  DashboardIcons.MCX_OIL_CHANGE_ICON },
  { key: 'roadside', label: 'Roadside Assistance', icon: DashboardIcons.MCX_ROADSIDE_ASSISTANCE_ICON },
  { key: 'diagnostics', label: 'Diagnostics & Inspection', icon: DashboardIcons.MCX_DIAGNOSTICS_INSPECTION_ICON },
  { key: 'repair', label: 'Repair', icon: DashboardIcons.MCX_REPAIR_ICON },
];

export const dbMechanicList = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    address: 'Edappally, Kochi',
    userRating: 5,
    ratingOutOf: 5,
    availability: 'Open',
    serviceTypes: ['oil_change', 'repair', 'diagnostics_inspection'],
    favourite: false,
  },
  {
    id: 2,
    name: 'Seby M MMM',
    address: 'Edappally, Kochi',
    userRating: 4,
    ratingOutOf: 5,
    availability: 'Appointment',
    serviceTypes: ['roadside_assistance', 'brake_service'],
    favourite: false,
  },
  {
    id: 3,
    name: 'Mobile Citrus',
    address: 'Edappally, Kochi',
    userRating: 4,
    ratingOutOf: 5,
    availability: 'Open',
    serviceTypes: ['oil_change', 'battery_service', 'repair'],
    favourite: true,
  },
  {
    id: 4,
    name: 'Mansoor M3',
    address: 'Edappally, Kochi',
    userRating: 4,
    ratingOutOf: 5,
    availability: 'Appointment',
    serviceTypes: ['diagnostics_inspection', 'repair'],
    favourite: false,
  },
  {
    id: 5,
    name: 'Mansoor M3',
    address: 'Edappally, Kochi',
    userRating: 4,
    ratingOutOf: 5,
    availability: 'Appointment',
    serviceTypes: ['oil_change', 'roadside_assistance', 'brake_service'],
    favourite: true,
  },
];

export const dbAvailabilityOptions = [
  'Open',
  'Appointment',
];

// Find Mechanic Filter Options
export const fmServiceTypeOptions = [
  { label: 'Oil Change', value: 'oil_change' },
  { label: 'Roadside Assistance', value: 'roadside_assistance' },
  { label: 'Diagnostics & Inspection', value: 'diagnostics_inspection' },
  { label: 'Repair', value: 'repair' },
  { label: 'Brake Service', value: 'brake_service' },
  { label: 'Battery Service', value: 'battery_service' },
];

export const fmAvailabilityOptions = [
  { label: 'Open', value: 'open' },
  { label: 'Appointment', value: 'appointment' },
  { label: 'Busy', value: 'busy' },
  { label: 'Available Now', value: 'available_now' },
];
export const ssScreeningQuestions = [
  {
    id: 'q1',
    question: 'Is your vehicle working?',
    type: 'dropdown',
    options: ['Yes', 'No'],
  },
  {
    id: 'q2',
    question: 'Is your vehicle parked in an accessible place where we can get to?',
    type: 'dropdown',
    options: ['Yes', 'No'],
  },
  {
    id: 'q3',
    question: 'Describe the issue (if any):',
    type: 'text',
    options: [],
  },
  {
    id: 'q4',
    question: 'Is your vehicle parked in an accessible place where we can get to?',
    type: 'dropdown',
    options: ['Yes', 'No'],
  },
];
export const userData = {
  name: 'Sebastian KP',
  firstName: 'Sebastian',
  lastName: 'Kp',
  email: '<EMAIL>',
  phone: '(*************',
  gender: 'Male',
  maritalStatus: 'Married',
  dateOfBirth: '01/01/1982',
  // Address information
  searchLocation: '',
  address1: 'railway station',
  address2: 'Aluva',
  city: 'Aluva',
  state: 'Kerala',
  zipCode: '683101',
  country: 'India',
  card: [{
    type: 'MasterCard',
    maskedNumber: '**** **** **** 6004',
    brandShort: 'MC',
  },
  {
    type: 'XXXXXCard',
    maskedNumber: '**** **** **** 6004',
    brandShort: 'MC',
  }
  ],
  vehicles: [
    { id: 1, name: 'BMW 525i' },
    { id: 2, name: 'Toyota Corolla' },
    { id: 3, name: 'Honda Accord' },
  ],
};
export const userLocationData = [
  {
    id: 1,
    type: 'current',
    title: AppStrings.MCX_CHOOSE_CURRENT_LOCATION_TEXT,
    isDefault: true,
  },
  {
    id: 2,
    type: 'saved',
    title: AppStrings.MCX_SAVED_LOCATION_TEXT,
    isDefault: false,
  },
  {
    id: 3,
    type: 'nearby',
    title: AppStrings.MCX_SEARCH_NEARBY_LOCATION_TEXT,
    isDefault: false,
  },
];
export const dbMenuItems = [
    { key: 'profile', label: 'My Profile' },
    { key: 'privacy_policy', label: 'Privacy Policy' },
    { key: 'terms_conditions', label: 'Terms & Conditions' },
    { key: 'refer_friend', label: 'Refer a Friend' },
    { key: 'help', label: 'Help' },
    { key: 'settings', label: 'Settings' },

];
export const ssPriceModelData = [
  // Empty array for now to show exception state
];

export const helpListData = [
  {
    id: 1,
    question: 'How Does MyCANx App Work?',
  },
  {
    id: 2,
    question: 'I Need An Enterprise Solution. I Am A Fleet Owner, Other Business Party, Corporation, Dealership, Or Other Auto Vendor, Who Can I Contact For More Information?',
  },
  {
    id: 3,
    question: 'How Do Mechanics Get Paid?',
  },
  {
    id: 4,
    question: 'Are Your Mechanics Certified And What Is The Vetting Process For New Mechanics?',
  },
  {
    id: 5,
    question: 'What Is Your Website?',
  },
  {
    id: 6,
    question: 'What Is Your Phone Number?',
  },
  {
    id: 7,
    question: 'What Are Your Terms Of Use And Privacy Policy?',
  },
  {
    id: 8,
    question: 'What Vehicles Do You Work On?',
  },
  {
    id: 9,
    question: 'Does MyCANx Provide Services For The Aviation Or Trucking Industry, Or Other Vehicle Maintenance Services?',
  },
  {
    id: 10,
    question: 'How Old Do I Have To Be To Use MyCANx Services?',
  },
  {
    id: 11,
    question: 'I Don\'t See MyCANx In My City. Why Isn\'t It Available Here?',
  },
  {
    id: 12,
    question: 'How Do I Pay For Service?',
  },
];
// Alert Confirmation Options
export const alertConfirmationOptions = [
  "Email notification only",
  "SMS notification only",
  "Email and SMS notification",
  "Phone call confirmation",
  "No notification required"
];

// Appointments Data
export const upcomingAppointments = [
  // Empty array for now to show exception state
];

export const otherAppointments = [
  // Empty array for now to show exception state
];
export const notificationSettings = [
  {
    label: 'In Application',
    value: 'inApplication',
    onChange: 'setInApplication',
  },
  {
    label: 'Text',
    value: 'textNotification',
    onChange: 'setTextNotification',
  },
  {
    label: AppStrings.MCX_EMAIL_TITLE,
    value: 'emailNotification',
    onChange: 'setEmailNotification',
  },
];
export const messageTabData = {
  ALL: 'ALL',
  SERVICES: 'SERVICES',
  SYSTEMS: 'SYSTEMS',
  PENDING: 'PENDING'
};

export const messageSection = {
  TYPES: 'Types',
  DETAILS: 'Details'
};
// Add this new menu drawer data
export const menuDrawerData = [
  { id: 'profile', label: 'Profile', icon: AppCommonIcons.MCX_PROFILE_ICON },
  { id: 'messages', label: 'Messages', icon:AppCommonIcons.MCX_MESSAGES_ICON},
  { id: 'find_mechanic', label: 'Find Mechanic', icon: AppCommonIcons.MCX_FIND_MECHANIC_ICON },
  { id: 'schedule_service', label: 'Schedule Service', icon: AppCommonIcons.MCX_SCHEDULE_SERVICE_ICON},
  { id: 'settings', label: 'Settings', icon: AppCommonIcons.MCX_SETTINGS_ICON},
  { id: 'help', label: 'Help', icon: AppCommonIcons.MCX_HELP_ICON },
   { id: 'refer_friend', label: 'Refer a friend', icon: AppCommonIcons.MCX_REFER_FRIEND_ICON_1 },
];
export const screenMap: { [key: string]: string } = {
  profile: 'MCX_NAV_USER_PROFILE',
  messages: 'MCX_NAV_MESSAGES',
  find_mechanic: 'MCX_FIND_MECHANIC',
  schedule_service: 'MCX_SCHEDULE',
  settings: 'MCX_NAV_SETTINGS',
  help: 'MCX_NAV_HELP',
  refer_friend: 'MCX_NAV_REFER_FRIEND',
  work_history: 'MCX_WORK_HISTORY',
};