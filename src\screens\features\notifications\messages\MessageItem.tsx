import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { AppCommonIcons } from '../../../../utils/constants/AppStrings';

interface MessageItemProps {
  id: string;
  title: string;
  content: string;
  date: string;
  isRead: boolean;
  onPress: (id: string) => void;
  onDelete: (id: string) => void;
}

const MessageItem: React.FC<MessageItemProps> = ({
  id,
  title,
  content,
  date,
  isRead,
  onPress,
  onDelete,
}) => {
  return (
    <TouchableOpacity
      style={[styles.container, !isRead && styles.unreadContainer]}
      onPress={() => onPress(id)}
    >
      <View style={styles.contentContainer}>
        <Text style={[styles.title, !isRead && styles.unreadText]}>{title}</Text>
        <Text style={styles.content} numberOfLines={2}>{content}</Text>
        <Text style={styles.date}>{date}</Text>
      </View>

      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => onDelete(id)}
      >
        <Image
          source={AppCommonIcons.MCX_DELETE_ICON}
          style={styles.deleteIcon}
        />
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 16,
  },
  deleteIcon: {
    width: 20,
    height: 20,
  },
  unreadContainer: {
    backgroundColor: '#F5F5F5',
  },
  contentContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    color: '#333333',
    marginBottom: 4,
  },
  unreadText: {
    fontWeight: 'bold',
  },
  content: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  date: {
    fontSize: 12,
    color: '#999999',
  },
  deleteButton: {
    justifyContent: 'center',
    padding: 8,
  },
});

export default MessageItem;