import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { AppCommonIcons, ExceptionStrings } from '../../../../utils/constants/AppStrings';
import { Colors, Sizes, Fonts } from '../../../../utils/constants/Theme';

interface EmptyPendingProps {
  message?: string;
}

const EmptyPending: React.FC<EmptyPendingProps> = ({ 
  message = "No pending tasks available"
}) => {
  return (
    <View style={styles.container}>
      <Image 
        source={AppCommonIcons.MCX_NO_MESSAGES_ICON} 
        style={styles.icon} 
      />
      <Text style={styles.text}>{message}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  icon: {
    width: 120,
    height: 120,
    marginBottom: 20,
  },
  text: {
    fontSize: Sizes.LARGE,
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    textAlign: 'center',
    fontWeight: '500',
    fontFamily: Fonts.ROBO_REGULAR,
  },
});

export default EmptyPending;
