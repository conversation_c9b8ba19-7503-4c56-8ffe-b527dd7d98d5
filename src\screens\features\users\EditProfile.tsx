import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Platform, Modal } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import Icon from 'react-native-vector-icons/MaterialIcons';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import TitleSection from '../../../components/common/TitleSection';
import CommonTextInput from '../../../components/common/CommonTextInput';
import CommonDropdown from '../../../components/common/CommonDropdown';
import CustomButton from '../../../components/common/CustomButton';
import HorizontalDivider from '../../../components/common/HorizontalDivider';
import { Colors, Fonts, Sizes, CommonUIParams } from '../../../utils/constants/Theme';
import { AppCommonIcons, AppStrings } from '../../../utils/constants/AppStrings';
import { userData } from '../../../utils/templates/TemplateConfig';

const EditProfile = () => {
  // Form state - Initialize with userData
  const [firstName, setFirstName] = useState(userData.firstName || '');
  const [lastName, setLastName] = useState(userData.lastName || '');
  const [email, setEmail] = useState(userData.email);
  const [mobile, setMobile] = useState(userData.phone);
  const [gender, setGender] = useState(userData.gender || '');
  const [maritalStatus, setMaritalStatus] = useState(userData.maritalStatus || '');
  const [dateOfBirth, setDateOfBirth] = useState(userData.dateOfBirth || '');

  // Date picker state
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerValue, setDatePickerValue] = useState(new Date(1982, 0, 1));

  // Address fields - Initialize with userData
  const [searchLocation, setSearchLocation] = useState(userData.searchLocation || '');
  const [address1, setAddress1] = useState(userData.address1 || '');
  const [address2, setAddress2] = useState(userData.address2 || '');
  const [city, setCity] = useState(userData.city || '');
  const [state, setState] = useState(userData.state || '');
  const [zipCode, setZipCode] = useState(userData.zipCode || '');
  const [country, setCountry] = useState(userData.country || '');

  // Dropdown options
  const genderOptions = [
    { label: 'Male', value: 'Male' },
    { label: 'Female', value: 'Female' },
    { label: 'Other', value: 'Other' },
  ];

  const maritalStatusOptions = [
    { label: 'Single', value: 'Single' },
    { label: 'Married', value: 'Married' },
    { label: 'Divorced', value: 'Divorced' },
    { label: 'Widowed', value: 'Widowed' },
  ];

  const handleDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }

    if (selectedDate) {
      setDatePickerValue(selectedDate);
      // Format date as MM/DD/YYYY
      const month = (selectedDate.getMonth() + 1).toString().padStart(2, '0');
      const day = selectedDate.getDate().toString().padStart(2, '0');
      const year = selectedDate.getFullYear().toString();
      const formattedDate = `${month}/${day}/${year}`;
      setDateOfBirth(formattedDate);

      if (Platform.OS === 'ios') {
        setShowDatePicker(false);
      }
    } else if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }
  };

  const handleChangePhoto = () => {
    // Handle profile photo change
    console.log('Change photo pressed');
  };

  const handleSaveProfile = () => {
    // Handle save profile
    console.log('Save profile pressed');
    console.log({
      firstName,
      lastName,
      email,
      mobile,
      gender,
      maritalStatus,
      dateOfBirth,
      searchLocation,
      address1,
      address2,
      city,
      state,
      zipCode,
      country,
    });
  };

  return (
    <View style={styles.mainContainer}>
      {/* Header */}
      <TitleSection
        title={AppStrings.MCX_EDIT_PROFILE_TITLE}
        bgColor={Colors.PRIMARY}
        textColor="#fff"
        style={styles.titleSection}
      />
      <ScreenLayout
        useScrollView={true}
        useImageBackground={true}
        centerContent={false}
        useHorizontalPadding={true}
        scrollContainerStyle={{ paddingBottom: 80 }}
      >

        {/* Single Form Container with Profile Photo and All Fields */}
        <View style={styles.formContainer}>
          {/* Profile Photo Section */}
          <View style={styles.profilePhotoSection}>
            <Image
              source={AppCommonIcons.MCX_USER_PROFILE_PIC}
              style={styles.profilePhoto}
            />
            <TouchableOpacity style={styles.changePhotoButton} onPress={handleChangePhoto}>
              <Text style={styles.changePhotoText}>{AppStrings.MCX_CHANGE_PHOTO_TEXT}</Text>
              <Image source={AppCommonIcons.MCX_ARROW_RIGHT} style={styles.changePhotoIcon} />
            </TouchableOpacity>
          </View>
          {/* First Name */}
          <Text style={styles.fieldLabel}>{AppStrings.MCX_FIRST_NAME_LABEL}</Text>
          <CommonTextInput
            value={firstName}
            onChangeText={setFirstName}
            placeholder={AppStrings.MCX_FIRST_NAME_PLACEHOLDER}
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
          />

          {/* Last Name */}
          <Text style={styles.fieldLabel}>{AppStrings.MCX_LAST_NAME_LABEL}</Text>
          <CommonTextInput
            value={lastName}
            onChangeText={setLastName}
            placeholder={AppStrings.MCX_LAST_NAME_PLACEHOLDER}
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
          />

          {/* Email */}
          <Text style={styles.fieldLabel}>{AppStrings.MCX_EMAIL_LABEL}</Text>
          <CommonTextInput
            value={email}
            onChangeText={setEmail}
            placeholder={AppStrings.MCX_EMAIL_PLACEHOLDER}
            keyboardType="email-address"
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
          />

          {/* Mobile */}
          <Text style={styles.fieldLabel}>{AppStrings.MCX_MOBILE_LABEL}</Text>
          <CommonTextInput
            value={mobile}
            onChangeText={setMobile}
            placeholder={AppStrings.MCX_MOBILE_PLACEHOLDER}
            keyboardType="phone-pad"
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
          />

          {/* Horizontal Divider Before Gender */}
          <HorizontalDivider isFullWidth={true} />

          {/* Gender and Marital Status Row */}
          <View style={styles.rowContainer}>
            <View style={styles.halfWidth}>
              <Text style={styles.rowFieldLabel}>{AppStrings.MCX_GENDER_LABEL}</Text>
              <CommonDropdown
                data={genderOptions}
                value={gender}
                onValueChange={setGender}
                placeholder={AppStrings.MCX_GENDER_PLACEHOLDER}
                style={styles.dropdown}
                fontWeight="800"
              />
            </View>
            <View style={styles.halfWidth}>
              <Text style={styles.rowFieldLabel}>{AppStrings.MCX_MARITAL_STATUS_LABEL}</Text>
              <CommonDropdown
                data={maritalStatusOptions}
                value={maritalStatus}
                onValueChange={setMaritalStatus}
                placeholder={AppStrings.MCX_MARITAL_STATUS_PLACEHOLDER}
                style={styles.dropdown}
                fontWeight="800"
              />
            </View>
          </View>

          {/* Date of Birth */}
          <Text style={styles.fieldLabel}>{AppStrings.MCX_DATE_OF_BIRTH_LABEL}</Text>
          <TouchableOpacity
            style={styles.dateInput}
            onPress={() => setShowDatePicker(true)}
          >
            <Text style={styles.dateInputText}>{dateOfBirth}</Text>
          </TouchableOpacity>

          {/* Search Location - No title, with elevation effect */}
          <View style={styles.searchLocationWrapper}>
            <View style={styles.searchInputContainer}>
              <Icon name="search" size={20} color={Colors.COMMON_GREY_SHADE_LIGHT} style={styles.searchIcon} />
              <CommonTextInput
                value={searchLocation}
                onChangeText={setSearchLocation}
                placeholder={AppStrings.MCX_SEARCH_LOCATION_PLACEHOLDER}
                style={styles.searchTextInput}
                placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
                fontSize={Sizes.LARGE}
              />
            </View>
          </View>

          {/* Address 1 */}
          <Text style={styles.fieldLabel}>{AppStrings.MCX_ADDRESS_1_LABEL}</Text>
          <CommonTextInput
            value={address1}
            onChangeText={setAddress1}
            placeholder={AppStrings.MCX_ADDRESS_1_PLACEHOLDER}
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
          />

          {/* Address 2 */}
          <Text style={styles.fieldLabel}>{AppStrings.MCX_ADDRESS_2_LABEL}</Text>
          <CommonTextInput
            value={address2}
            onChangeText={setAddress2}
            placeholder={AppStrings.MCX_ADDRESS_2_PLACEHOLDER}
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
          />

          {/* City */}
          <Text style={styles.fieldLabel}>{AppStrings.MCX_CITY_LABEL}</Text>
          <CommonTextInput
            value={city}
            onChangeText={setCity}
            placeholder={AppStrings.MCX_CITY_PLACEHOLDER}
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
          />
          {/* Horizontal Divider Before State and Zip Code Row */}
          <HorizontalDivider isFullWidth={true} />
          {/* State and Zip Code Row */}
          <View style={styles.rowContainer}>
            <View style={styles.halfWidth}>
              <Text style={styles.rowFieldLabel}>{AppStrings.MCX_STATE_LABEL}</Text>
              <CommonTextInput
                value={state}
                onChangeText={setState}
                placeholder={AppStrings.MCX_STATE_PLACEHOLDER}
                style={styles.rowTextInput}
                placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
                fontSize={Sizes.LARGE}
              />
            </View>
            <View style={styles.halfWidth}>
              <Text style={styles.rowFieldLabel}>{AppStrings.MCX_ZIP_CODE_LABEL}</Text>
              <CommonTextInput
                value={zipCode}
                onChangeText={setZipCode}
                placeholder={AppStrings.MCX_ZIP_CODE_PLACEHOLDER}
                keyboardType="numeric"
                style={styles.rowTextInput}
                placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
                fontSize={Sizes.LARGE}
              />
            </View>
          </View>

          {/* Country */}
          <Text style={styles.fieldLabel}>{AppStrings.MCX_COUNTRY_LABEL}</Text>
          <CommonTextInput
            value={country}
            onChangeText={setCountry}
            placeholder={AppStrings.MCX_COUNTRY_PLACEHOLDER}
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
          />
        </View>

        {/* Date Picker Modal */}
        {Platform.OS === 'ios' ? (
          showDatePicker && (
            <DateTimePicker
              value={datePickerValue}
              mode="date"
              display="default"
              onChange={handleDateChange}
              style={{ width: 150, height: 200 }}
            />
          )
        ) : (
          <Modal
            transparent={true}
            animationType="slide"
            visible={showDatePicker}
            onRequestClose={() => setShowDatePicker(false)}
          >
            <View style={styles.modalContainer}>
              <View style={styles.modalContent}>
                <DateTimePicker
                  value={datePickerValue}
                  mode="date"
                  display="default"
                  onChange={handleDateChange}
                />
              </View>
            </View>
          </Modal>
        )}

        {/* Save Button - Bottom Fixed */}
        <View style={styles.bottomButtonContainer}>
          <CustomButton
            text={AppStrings.MCX_SAVE_MY_PROFILE_BUTTON}
            onPress={handleSaveProfile}
            variant="primary"
            size="large"
            fullWidth={true}
            backgroundColor={Colors.SECONDARY}
            textColor="#fff"
            isBoldText={true}
            isBottomButton={true}
            bottomLineWidth={1}
            bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
          />
        </View>
      </ScreenLayout>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,/*  */
  },
  titleSection: {
    marginBottom: 0,
  },
  profilePhotoSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  profilePhoto: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  changePhotoButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  changePhotoText: {
    fontSize: Sizes.XSMALL,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.COMMON_GREY_SHADE_LIGHT,
    marginRight: 8,
    fontWeight: '600',
  },
  changePhotoIcon: {
    width: 32,
    height: 32,
    tintColor: Colors.PRIMARY,
  },
  formContainer: {
    backgroundColor: 'white',
    marginTop: 20,
    marginBottom: 4,
    paddingVertical: 0,
    borderRadius: 2,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  fieldLabel: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_BOLD,
    color: Colors.COMMON_HEADING_COLOR_1,
    marginTop: 16,
    marginLeft: CommonUIParams.CUSTOM_PADDING_16,
    marginBottom: 1,
    fontWeight: '600',
  },
  textInput: {
    marginTop: 0,
    marginBottom: 25,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: CommonUIParams.CUSTOM_PADDING_16,
    marginBottom: 16,
  },
  halfWidth: {
    flex: 0.48,
  },
  rowFieldLabel: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.COMMON_HEADING_COLOR_1,
    marginTop: 16,
    marginLeft: 0,
    fontWeight: '600',
  },
  dropdown: {
    marginLeft: 0,
    marginRight: 0,

  },
  rowTextInput: {
    marginLeft: 0,
    marginRight: 0,
    marginTop: 1,
    marginBottom: 0,
  },
  dateInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 2,
    padding: 8,
    marginLeft: CommonUIParams.CUSTOM_PADDING_16,
    marginRight: CommonUIParams.CUSTOM_PADDING_16,
    backgroundColor: '#fff',
    justifyContent: 'center',
    minHeight: 40,
    marginBottom: 25,
  },
  dateInputText: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.SECONDARY,
    fontWeight: '800',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  buttonContainer: {
    paddingVertical: 20,
    paddingHorizontal: 0,
  },
  bottomButtonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.COMMON_GREY_SHADE_DARK,
    paddingVertical: 0,
    paddingHorizontal: 0,
  },
  searchLocationWrapper: {
    backgroundColor: Colors.COMMON_GREY_SHADOW_LIGHT_COLOR,
    borderRadius: 2,
    marginHorizontal: CommonUIParams.CUSTOM_PADDING_16,
    marginBottom: 16,
    padding: 6,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 2,
    paddingHorizontal: 12,
    minHeight: 40,
    backgroundColor: '#fff',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchTextInput: {
    flex: 1,
    marginLeft: 0,
    marginRight: 0,
    marginTop: 0,
    marginBottom: 0,
    borderWidth: 0,
  },
});

export default EditProfile;
