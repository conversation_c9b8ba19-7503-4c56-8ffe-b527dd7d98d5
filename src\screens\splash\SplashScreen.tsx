import React, { useEffect } from 'react';
import { View, Image, StyleSheet } from 'react-native';
import { hp, wp } from '../../utils/ResponsiveParams';
import { StackNavigationProp } from '@react-navigation/stack';
import { AppCommonIcons } from '../../utils/constants/AppStrings';

type RootStackParamList = {
  LoginMainScreen: undefined;
};

type SplashScreenProps = {
  navigation: StackNavigationProp<RootStackParamList, 'LoginMainScreen'>;
};

const SplashScreen: React.FC<SplashScreenProps> = ({ navigation }) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      navigation.replace('LoginMainScreen');
    }, 2000);
    return () => clearTimeout(timer);
  }, [navigation]);

  return (
    <View style={styles.container}>
      <Image
        source={AppCommonIcons.MCX_APP_ICON}
        style={styles.logo}
        resizeMode="contain"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    width: wp(60),
    height: hp(30),
  },
});

export default SplashScreen;
