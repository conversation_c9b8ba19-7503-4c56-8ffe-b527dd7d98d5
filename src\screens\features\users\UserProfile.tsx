import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Colors, Fonts, Sizes } from '../../../utils/constants/Theme';
import { AppCommonIcons, AppStrings, RouteNames } from '../../../utils/constants/AppStrings';
import TitleSection from '../../../components/common/TitleSection';
import CommonCardStyle from '../../../components/common/CommonCardStyle';
import HorizontalDivider from '../../../components/common/HorizontalDivider';
import { userData } from '../../../utils/templates/TemplateConfig';
import PaymentCardComponent from '../../../components/cardstyles/PaymentCardComponent';
import CustomCardListStyle from '../../../components/cardstyles/CustomCardListStyle';
import ScreenLayout from '../../../components/layout/ScreenLayout';

const UserProfile = () => {
    const navigation = useNavigation();

    const handleEditProfile = () => {
        // Navigate to EditProfile screen
        navigation.navigate(RouteNames.MCX_NAV_EDIT_PROFILE as never);
    };

    const handleAddPaymentMethod = () => {
        // Handle add payment method navigation
        console.log('Add payment method pressed');
    };

    const handleDeleteCard = (cardIndex: number) => {
        // Handle card deletion
        console.log('Delete card at index:', cardIndex);
    };

    const handleDeleteAccount = () => {
        // Handle account deletion
        console.log('Delete account pressed');
    };
    const handleAddVehicles = () => {
        // Handle add vehicles navigation
        console.log('Add vehicles pressed');
    };

    const handleVehiclePress = (vehicleId: number) => {
        // Handle vehicle selection/edit
        console.log('Vehicle pressed:', vehicleId);
    };

    // Fixed bottom content
    const fixedBottomContent = (
        <View style={styles.deleteAccountButton}>
            <Text style={styles.deleteAccountText}>{AppStrings.MCX_DELETE_ACCOUNT_TEXT}</Text>
            <TouchableOpacity style={styles.deleteButtonContainer} onPress={handleDeleteAccount}>
                <Text style={styles.deleteButtonText}>{AppStrings.MCX_DELETE_ACCOUNT_KEYWORD}</Text>
            </TouchableOpacity>
        </View>
    );

    return (
        <ScreenLayout
            useScrollView={true}
            useImageBackground={true}
            centerContent={true}
            fixedBottomContent={fixedBottomContent}
        >
            {/* Header */}
            <TitleSection
                title={AppStrings.MCX_USER_PROFILE_Title}
                bgColor={Colors.PRIMARY}
                textColor="#fff"
            />
                    {/* MY DETAILS Section */}
                    <CommonCardStyle
                        header={AppStrings.MCX_USER_DETAIL_Text}
                        headerColor={Colors.SECONDARY}
                        textColor={Colors.COMMON_WHITE_SHADE}
                        isCardContainerDecorated={true}
                        isTitleBordered={true}
                        cardBackgroundColor="white"
                    >
                        {/* User Profile Header */}
                        <View style={styles.profileHeader}>
                            <Image
                                source={AppCommonIcons.MCX_USER_PROFILE_PIC}
                                style={styles.profileAvatar}
                            />
                            <View style={styles.profileInfo}>
                                <Text style={styles.profileName}>{userData.name}</Text>
                                <Text style={styles.profileEmail}>{userData.email}</Text>
                            </View>
                        </View>
                        <HorizontalDivider />

                        {/* Phone Number Row */}
                        <CustomCardListStyle
                            title={userData.phone}
                            rightText={AppStrings.MCX_EDIT_PROFILE_DETAILS_TEXT}
                            rightElementType="image"
                            rightIcon={AppCommonIcons.MCX_ARROW_RIGHT}
                            rightIconStyle={styles.cardIconStyle}
                            onPress={handleEditProfile}
                            textColor={Colors.COMMON_GREY_SHADE_LIGHT}
                            subtitleColor={Colors.COMMON_GREY_SHADE_LIGHT}
                            showHorizontalLine={true}
                            horizontalLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
                            rightTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
                        />

                        {/* Add Payment Method Row */}
                        <CustomCardListStyle
                            title={AppStrings.MCX_ADD_PAYMENT_METHOD_TEXT}
                            rightElementType="image"
                            rightIcon={AppCommonIcons.MCX_ARROW_RIGHT}
                            rightIconStyle={styles.cardIconStyle}
                            onPress={handleAddPaymentMethod}
                            textColor={Colors.COMMON_GREY_SHADE_LIGHT}
                        />
                        {/* Payment Cards */}
                        {userData.card && userData.card.length > 0 && (
                            <>
                                <HorizontalDivider />
                                {userData.card.map((card, index) => (
                                    <React.Fragment key={index}>
                                        <PaymentCardComponent
                                            card={card}
                                            onDelete={() => handleDeleteCard(index)}
                                        />
                                        {index < userData.card.length - 1 && <HorizontalDivider />}
                                    </React.Fragment>
                                ))}
                            </>
                        )}
                    </CommonCardStyle>

                    {/* MY VEHICLES Section */}
                    <CommonCardStyle
                        header={AppStrings.MCX_MY_VEHICLE_TEXT}
                        headerColor={Colors.SECONDARY}
                        textColor={Colors.COMMON_GREY_SHADE_LIGHT}
                        isCardContainerDecorated={true}
                        isTitleBordered={true}
                        cardBackgroundColor="white"
                    >
                        <CustomCardListStyle
                            title={AppStrings.MCX_USER_PROFILE_ADD_VEHICLES}
                            rightElementType="image"
                            rightIcon={AppCommonIcons.MCX_ARROW_RIGHT}
                            rightIconStyle={styles.cardIconStyle}
                            onPress={handleAddVehicles}
                            textColor={Colors.COMMON_GREY_SHADE_LIGHT}
                        />

                        {/* Vehicle List from userData */}
                        {userData.vehicles && userData.vehicles.length > 0 && (
                            <>
                                <HorizontalDivider />
                                {userData.vehicles.map((vehicle, index) => (
                                    <React.Fragment key={vehicle.id}>
                                        <CustomCardListStyle
                                            title={vehicle.name}
                                            rightElementType="image"
                                            rightIconStyle={styles.cardIconStyle}
                                            onPress={() => handleVehiclePress(vehicle.id)}
                                            textColor={Colors.COMMON_GREY_SHADE_LIGHT}
                                        />
                                        {index < userData.vehicles.length - 1 && <HorizontalDivider />}
                                    </React.Fragment>
                                ))}
                            </>
                        )}
                    </CommonCardStyle>
        </ScreenLayout>
    );
};

const styles = StyleSheet.create({
    // Profile Header Styles
    profileHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 8,
        paddingHorizontal: 8,
    },
    profileAvatar: {
        width: 50,
        height: 50,
        borderRadius: 25,
        marginRight: 12,
    },
    profileInfo: {
        flex: 1,
    },
    profileName: {
        fontSize: Sizes.XLARGE,
        fontFamily: Fonts.ROBO_BOLD,
        fontWeight: 'bold',
        color: Colors.SECONDARY,
    },
    profileEmail: {
        fontSize: Sizes.LARGE,
        fontFamily: Fonts.ROBO_REGULAR,
        color: Colors.PRIMARY,
    },
    // Profile Row Styles
    profileRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 10,
        paddingHorizontal: 8,
    },
    profileRowText: {
        fontSize: Sizes.LARGE,
        fontFamily: Fonts.ROBO_REGULAR,
        color: Colors.SECONDARY,
        flex: 1,
    },
    profileRowRight: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    profileRowRightText: {
        fontSize: Sizes.SMALL,
        fontFamily: Fonts.ROBO_REGULAR,
        color: Colors.COMMON_GREY_SHADE_LIGHT,
        marginRight: 8,
    },
    editText: {
        color: Colors.COMMON_GREY_SHADE_LIGHT,
    },
    arrowIcon: {
        width: 20,
        height: 20,
        tintColor: Colors.PRIMARY,
    },

    // Payment Card Styles
    paymentCardRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 12,
        paddingHorizontal: 8,
    },
    cardContent: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    cardIcon: {
        width: 40,
        height: 25,
        marginRight: 12,
    },
    cardNumber: {
        fontSize: Sizes.LARGE,
        fontFamily: Fonts.ROBO_REGULAR,
        color: Colors.SECONDARY,
    },
    deleteIcon: {
        width: 20,
        height: 20,
        tintColor: Colors.COMMON_GREY_SHADE_LIGHT,
    },
    deleteAccountButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    deleteAccountText: {
        fontSize: Sizes.LARGE,
        fontFamily: Fonts.ROBO_REGULAR,
        color: Colors.SECONDARY,
    },
    deleteButtonContainer: {
        backgroundColor: Colors.PRIMARY,
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 4,
    },
    deleteButtonText: {
        fontSize: Sizes.SMALL,
        fontFamily: Fonts.ROBO_BOLD,
        fontWeight: 'bold',
        color: '#fff',
    },
    cardIconStyle: {
        width: 20,
        height: 20,
        tintColor: Colors.PRIMARY,
    },
});

export default UserProfile;