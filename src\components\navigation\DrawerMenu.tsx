import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Image, 
  ScrollView
} from 'react-native';
import { menuDrawerData } from '../../utils/templates/TemplateConfig';
import { useNavigation } from '@react-navigation/native';

interface DrawerMenuProps {
  onClose: () => void;
}

const DrawerMenu: React.FC<DrawerMenuProps> = ({ onClose }) => {
  const navigation = useNavigation();

  const handleMenuItemPress = (id: string) => {
    // Map menu item IDs to screen names
    const screenMap: {[key: string]: string} = {
      'profile': 'MCX_NAV_USER_PROFILE',
      'messages': 'MCX_NAV_MESSAGES',
      'find_mechanic': 'MCX_FIND_MECHANIC',
      'schedule_service': 'MCX_SCHEDULE',
      'settings': 'MCX_NAV_SETTINGS',
      'help': 'MCX_NAV_HELP',
      'refer_friend': 'MCX_NAV_REFER_FRIEND'
    };
    
    const screenName = screenMap[id];
    if (screenName) {
      navigation.navigate(screenName as never);
      onClose();
    }
  };

  const handleLogout = () => {
    navigation.navigate('LoginMainScreen' as never);
    onClose();
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.menuItems}>
        {menuDrawerData.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={styles.menuItem}
            onPress={() => handleMenuItemPress(item.id)}
          >
            <Image source={item.icon} style={styles.menuIcon} />
            <Text style={styles.menuText}>{item.label}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
      
      <View style={styles.footer}>
        <Text style={styles.versionText}>v1.5.4</Text>
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutText}>LOGOUT</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1e2a36',
  },
  menuItems: {
    flex: 1,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  menuIcon: {
    width: 24,
    height: 24,
    marginRight: 16,
    tintColor: '#e74c3c',
  },
  menuText: {
    color: '#a7b6c2',
    fontSize: 16,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
    borderTopWidth: 0.5,
    borderTopColor: 'rgba(255,255,255,0.1)',
  },
  versionText: {
    color: '#a7b6c2',
    fontSize: 12,
    marginBottom: 10,
  },
  logoutButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#fff',
    borderRadius: 25,
    paddingVertical: 10,
    paddingHorizontal: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoutText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default DrawerMenu;



