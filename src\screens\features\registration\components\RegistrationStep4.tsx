import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';

const RegistrationStep4 = () => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [emergencyContact, setEmergencyContact] = useState('');
  const [emergencyPhone, setEmergencyPhone] = useState('');

  const handleContinue = () => {
    console.log('Step 4 - Continue pressed');
  };

  return (
    <View style={styles.container}>
      <View style={styles.formSection}>
        <RegistrationTitleSection
          title="Contact Information"
          backgroundColor="#FFFFFF"
          borderBottomWidth={1}
          borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
          paddingVertical={16}
          paddingHorizontal={0}
        />

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Phone Number</Text>
          <CommonTextInput
            value={phoneNumber}
            onChangeText={setPhoneNumber}
            placeholder="Enter your phone number"
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            keyboardType="phone-pad"
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Emergency Contact Name</Text>
          <CommonTextInput
            value={emergencyContact}
            onChangeText={setEmergencyContact}
            placeholder="Enter emergency contact name"
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Emergency Contact Phone</Text>
          <CommonTextInput
            value={emergencyPhone}
            onChangeText={setEmergencyPhone}
            placeholder="Enter emergency contact phone"
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            keyboardType="phone-pad"
          />
        </View>
      </View>

      <View style={styles.bottomContainer}>
        <CustomButton
          text="CONTINUE"
          onPress={handleContinue}
          variant="outline"
          size="large"
          backgroundColor="transparent"
          textColor={Colors.COMMON_GREY_SHADE_LIGHT}
          borderColor={Colors.COMMON_GREY_SHADE_LIGHT}
          borderWidth={1}
          style={styles.continueButton}
          isBoldText={true}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 0,
    justifyContent: 'flex-start',
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 12,
    borderRadius: 2,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    alignSelf: 'stretch',
    minHeight: 'auto',
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: Sizes.LARGE,
    fontWeight: '600',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
    marginBottom: 20,
  },
  inputContainer: {
    marginBottom: 20,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  inputLabel: {
    fontSize: Sizes.MEDIUM,
    fontWeight: '500',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 20,
    left: 16,
    right: 16,
  },
  continueButton: {
    borderRadius: 4,
  },
});

export default RegistrationStep4;
