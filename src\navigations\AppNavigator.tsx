import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import SplashScreen from '../screens/splash/SplashScreen';
import LoginMainScreen from '../screens/login/LoginMainScreen';
import AppLoginPageScreen from '../screens/login/AppLoginPageScreen';
import DrawerNavigator from './DrawerNavigator';
import UserProfile from '../screens/features/users/UserProfile';
import Schedule from '../screens/features/schedule/Schedule';
import SettingsScreen from '../screens/features/settings/SettingsScreen';
import MessagesScreen from '../screens/features/notifications/messages/MessagesScreen';
import FindMechanics from '../screens/features/find_mechanics/FindMechanics';
import Help from '../screens/features/legal/Help';
import ReferFriend from '../screens/features/referral/ReferFriend';
import AccountRegistration from '../screens/features/registration/AccountRegistration';
import { RouteNames } from '../utils/constants/AppStrings';

const Stack = createNativeStackNavigator();

const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="Splash" screenOptions={{ headerShown: false }}>
        <Stack.Screen name="Splash" component={SplashScreen} />
        <Stack.Screen name="LoginMainScreen" component={LoginMainScreen} />
        <Stack.Screen name="AppLoginPageScreen" component={AppLoginPageScreen} />
        <Stack.Screen name="DashBoard" component={DrawerNavigator} />
        {/* These screens are accessible from both stack and drawer navigation */}
        <Stack.Screen name="MCX_NAV_USER_PROFILE" component={UserProfile} />
        <Stack.Screen name="MCX_NAV_MESSAGES" component={MessagesScreen} />
        <Stack.Screen name="MCX_FIND_MECHANIC" component={FindMechanics} />
        <Stack.Screen name="MCX_SCHEDULE" component={Schedule} />
        <Stack.Screen name="MCX_NAV_SETTINGS" component={SettingsScreen} />
        <Stack.Screen name="MCX_NAV_HELP" component={Help} />
        <Stack.Screen name="MCX_NAV_REFER_FRIEND" component={ReferFriend} />
        <Stack.Screen name={RouteNames.MCX_NAV_ACCOUNT_REGISTRATION} component={AccountRegistration} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
