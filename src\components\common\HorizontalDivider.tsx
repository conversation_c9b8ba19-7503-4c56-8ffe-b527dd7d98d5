import { StyleSheet, View } from 'react-native';
import { Colors } from '../../utils/constants/Theme';

interface HorizontalDividerProps {
    isFullWidth?: boolean;
    color?: string;
    height?: number;
}

const HorizontalDivider = ({ isFullWidth = false, color, height = 1 }: HorizontalDividerProps) => (
    <View
         style={[
            styles.horizontalDivider,
            isFullWidth && styles.fullWidthDivider,
            color && { backgroundColor: color },
            { height: height || 1 }
        ]}
    />
);

const styles = StyleSheet.create({
    horizontalDivider: {
        height: 1,
        backgroundColor: Colors.COMMON_GREY_SHADE_LIGHT,
        opacity: 0.4,
        shadowColor: Colors.COMMON_GREY_SHADE_LIGHT,
        shadowOffset: { width: 0, height: 0.5 },
        shadowOpacity: 0.2,
        shadowRadius: 1,
        elevation: 1,
    },
    fullWidthDivider: {
        width: '100%',
        height: 1,
        backgroundColor: Colors.DIVIDER_COLOR,
        opacity: 0.3,
        shadowColor: Colors.DIVIDER_COLOR,
        shadowOffset: { width: 0, height: 0.5 },
        shadowOpacity: 0.2,
        shadowRadius: 1,
        elevation: 1,
    },
});

export default HorizontalDivider;
