import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import { AppStrings } from '../../../../utils/constants/AppStrings';
import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';

const RegistrationStep3 = () => {
  const [vinNumber, setVinNumber] = useState('');

  const handleLoadVin = () => {
    if (!vinNumber.trim()) {
      Alert.alert('Error', 'Please enter a VIN number');
      return;
    }
    // Handle VIN loading logic
    console.log('Loading VIN:', vinNumber);
    Alert.alert('Success', 'VIN loaded successfully');
  };

  const handleContinue = () => {
    // Handle continue logic
    console.log('Continue pressed');
  };

  return (
    <View style={styles.container}>
      {/* Vehicle Section - Growing Container */}
      <View style={styles.vehicleSection}>
        <RegistrationTitleSection
          title={AppStrings.MCX_MY_VEHICLE_TITLE}
          backgroundColor="#FFFFFF"
          borderBottomWidth={1}
          borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
          paddingVertical={16}
          paddingHorizontal={0}
        />

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>{AppStrings.MCX_VIN_LABEL}</Text>
          <CommonTextInput
            value={vinNumber}
            onChangeText={setVinNumber}
            placeholder="Enter VIN number"
            style={styles.vinInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
          />
        </View>

        <CustomButton
          text={AppStrings.MCX_LOAD_VIN_BUTTON}
          onPress={handleLoadVin}
          variant="primary"
          size="large"
          backgroundColor={Colors.PRIMARY}
          textColor="#fff"
          style={styles.loadVinButton}
          isBoldText={true}
        />
      </View>

      {/* Continue Button - Fixed at Bottom of Screen */}
      <View style={styles.bottomContainer}>
        <CustomButton
            text={AppStrings.MCX_CONTINUE_BUTTON}
            onPress={handleContinue}
            variant="primary"
            size="large"
            fullWidth={true}
            backgroundColor={Colors.SECONDARY}
            textColor="#fff"
            isBoldText={true}
            isBottomButton={true}
            bottomLineWidth={1}
            bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
          />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
  },
  vehicleSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 10,
    borderRadius: 2,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    alignSelf: 'stretch',
    minHeight: 'auto',
    overflow: 'hidden',
  },
  inputContainer: {
    marginBottom: 20,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  inputLabel: {
    fontSize: Sizes.MEDIUM,
    fontWeight: '500',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 8,
  },
  vinInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  loadVinButton: {
    marginTop: 10,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 4,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  continueButton: {
    borderRadius: 4,
    paddingVertical: 14,
  },
});

export default RegistrationStep3;
