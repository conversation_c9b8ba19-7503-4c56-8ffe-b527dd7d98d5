import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import { AppStrings } from '../../../../utils/constants/AppStrings';
import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';

const RegistrationStep3 = () => {
  const [vinNumber, setVinNumber] = useState('');

  const handleLoadVin = () => {
    if (!vinNumber.trim()) {
      Alert.alert('Error', 'Please enter a VIN number');
      return;
    }
    // Handle VIN loading logic
    console.log('Loading VIN:', vinNumber);
    Alert.alert('Success', 'VIN loaded successfully');
  };

  const handleContinue = () => {
    // Handle continue logic
    console.log('Continue pressed');
  };

  return (
    <View style={styles.container}>
      {/* Vehicle Section */}
      <View style={styles.vehicleSection}>
        <Text style={styles.sectionTitle}>{AppStrings.MCX_MY_VEHICLE_TITLE}</Text>
        
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>{AppStrings.MCX_VIN_LABEL}</Text>
          <CommonTextInput
            value={vinNumber}
            onChangeText={setVinNumber}
            placeholder="Enter VIN number"
            style={styles.vinInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
          />
        </View>

        <CustomButton
          text={AppStrings.MCX_LOAD_VIN_BUTTON}
          onPress={handleLoadVin}
          variant="primary"
          size="large"
          backgroundColor={Colors.PRIMARY}
          textColor="#fff"
          style={styles.loadVinButton}
          isBoldText={true}
        />
      </View>

      {/* Continue Button at Bottom */}
      <View style={styles.bottomContainer}>
        <CustomButton
          text={AppStrings.MCX_CONTINUE_BUTTON}
          onPress={handleContinue}
          variant="outline"
          size="large"
          backgroundColor="transparent"
          textColor={Colors.COMMON_GREY_SHADE_LIGHT}
          borderColor={Colors.COMMON_GREY_SHADE_LIGHT}
          borderWidth={1}
          style={styles.continueButton}
          isBoldText={true}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  vehicleSection: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    marginTop: 20,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  sectionTitle: {
    fontSize: Sizes.LARGE,
    fontWeight: '600',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
    marginBottom: 20,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: Sizes.MEDIUM,
    fontWeight: '500',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 8,
  },
  vinInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  loadVinButton: {
    marginTop: 10,
    borderRadius: 4,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 20,
    left: 16,
    right: 16,
  },
  continueButton: {
    borderRadius: 4,
  },
});

export default RegistrationStep3;
