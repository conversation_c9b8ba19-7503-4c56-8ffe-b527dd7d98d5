import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { Colors, CommonUIParams, Fonts, Sizes } from '../../utils/constants/Theme';

interface CommonRadioGroupProps {
    options: string[];
    selectedValue: string | null;
    onValueChange: (value: string) => void;
    disabled?: boolean;
    radioButtonColor?: string;
    textColor?: string;
    containerStyle?: object;
}

const CommonRadioGroup = ({
    options,
    selectedValue,
    onValueChange,
    disabled = false,
    radioButtonColor = Colors.COMMON_GREY_SHADE_DARK,
    textColor = Colors.SECONDARY,
    containerStyle,
}: CommonRadioGroupProps) => {
    return (
        <View style={[styles.radioGroup, containerStyle]}>
            {options.map(option => (
                <TouchableOpacity
                    key={option}
                    style={styles.radioOption}
                    onPress={() => !disabled && onValueChange(option)}
                    disabled={disabled}
                >
                    <View style={[
                        styles.radioCircle,
                        { borderColor: radioButtonColor },
                        selectedValue === option && { backgroundColor: radioButtonColor },
                        disabled && styles.radioCircleDisabled,
                    ]} />
                    <Text style={[
                        styles.radioLabel,
                        { color: textColor },
                        disabled && styles.radioLabelDisabled,
                    ]}>
                        {option}
                    </Text>
                </TouchableOpacity>
            ))}
        </View>
    );
};

const styles = StyleSheet.create({
    radioGroup: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginVertical: 6,
        marginLeft: CommonUIParams.CUSTOM_PADDING_16,
    },
    radioOption: {
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: 16,
        marginBottom: 6,
    },
    radioCircle: {
        width: 18,
        height: 18,
        borderRadius: 9,
        borderWidth: 2,
        marginRight: 6,
        backgroundColor: '#fff',
    },
    radioCircleDisabled: {
        opacity: 0.5,
    },
    radioLabel: {
        fontSize: Sizes.SMALL,
        fontFamily: Fonts.ROBO_REGULAR,
    },
    radioLabelDisabled: {
        opacity: 0.5,
    },
});

export default CommonRadioGroup;
