import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';

const RegistrationStep1 = () => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');

  const handleContinue = () => {
    console.log('Step 1 - Continue pressed');
  };

  return (
    <View style={styles.container}>
      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>Personal Information</Text>
        
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>First Name</Text>
          <CommonTextInput
            value={firstName}
            onChangeText={setFirstName}
            placeholder="Enter your first name"
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Last Name</Text>
          <CommonTextInput
            value={lastName}
            onChangeText={setLastName}
            placeholder="Enter your last name"
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Email Address</Text>
          <CommonTextInput
            value={email}
            onChangeText={setEmail}
            placeholder="Enter your email address"
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            keyboardType="email-address"
          />
        </View>
      </View>

      <View style={styles.bottomContainer}>
        <CustomButton
          text="CONTINUE"
          onPress={handleContinue}
          variant="outline"
          size="large"
          backgroundColor="transparent"
          textColor={Colors.COMMON_GREY_SHADE_LIGHT}
          borderColor={Colors.COMMON_GREY_SHADE_LIGHT}
          borderWidth={1}
          style={styles.continueButton}
          isBoldText={true}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    marginTop: 20,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  sectionTitle: {
    fontSize: Sizes.LARGE,
    fontWeight: '600',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
    marginBottom: 20,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: Sizes.MEDIUM,
    fontWeight: '500',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 20,
    left: 16,
    right: 16,
  },
  continueButton: {
    borderRadius: 4,
  },
});

export default RegistrationStep1;
