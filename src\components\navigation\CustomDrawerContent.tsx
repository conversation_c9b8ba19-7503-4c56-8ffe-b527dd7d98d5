import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  SafeAreaView,
  FlatList,
} from 'react-native';
import { DrawerContentComponentProps } from '@react-navigation/drawer';
import { menuDrawerData } from '../../utils/templates/TemplateConfig';
import { RouteNames } from '../../utils/constants/AppStrings';
import CustomButton from '../common/CustomButton';
import { Colors, Fonts, Sizes } from '../../utils/constants/Theme';
import { AppCommonIcons, AppStrings } from '../../utils/constants/AppStrings';

const CustomDrawerContent = (props: DrawerContentComponentProps) => {
  const { navigation } = props;

  const handleLogout = () => {
    navigation.navigate('LoginMainScreen' as never);
  };

  const renderMenuItem = ({ item, index }: { item: any; index: number }) => (
    <React.Fragment>
      <TouchableOpacity
        style={styles.menuItem}
        onPress={() => {
          // Create a proper mapping using RouteNames constants
          const routeMap: { [key: string]: string } = {
            'profile': RouteNames.MCX_NAV_USER_PROFILE,
            'messages': RouteNames.MCX_NAV_MESSAGES,
            'find_mechanic': RouteNames.MCX_FIND_MECHANIC,
            'schedule_service': RouteNames.MCX_SCHEDULE,
            'settings': RouteNames.MCX_NAV_SETTINGS,
            'help': RouteNames.MCX_NAV_HELP,
            'refer_friend': RouteNames.MCX_NAV_REFER_FRIEND,
            'account_registration': RouteNames.MCX_NAV_ACCOUNT_REGISTRATION,
          };

          const screenName = routeMap[item.id];
          if (screenName) {
            navigation.navigate(screenName as never);
          }
        }}
        activeOpacity={0.7}
      >
        <View style={styles.iconContainer}>
          <Image source={item.icon} style={styles.menuIcon} />
        </View>
        <Text style={styles.menuText}>{item.label}</Text>
      </TouchableOpacity>
      {/* Divider after each item except the last */}
      {index < menuDrawerData.length - 1 && (
        <View style={styles.menuDivider} />
      )}
    </React.Fragment>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Custom Header */}
      <View style={styles.header}>
        <Image
          source={AppCommonIcons.MCX_MYCANX_LOGO_TEXT}
          style={styles.logo}
          resizeMode="contain"
        />
      </View>
      {/* FlatList for menu items */}
      <FlatList
        data={menuDrawerData}
        renderItem={renderMenuItem}
        keyExtractor={item => item.id}
        showsVerticalScrollIndicator={false}
        ListFooterComponent={
          <View style={styles.logoutButtonContainer}>
            <CustomButton
              text={AppStrings.MCX_LOGOUT_TEXT}
              onPress={handleLogout}
              variant="outline"
              size="medium"
              backgroundColor="transparent"
              textColor="#fff"
              borderColor="#fff"
              borderWidth={1}
              style={styles.logoutButton}
              textStyle={styles.logoutText}
              isBoldText={true}
            />
          </View>
        }
      />
      {/* Version text at the very bottom */}
      <View style={styles.versionContainer}>
        <Text style={styles.versionText}>v1.5.4</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1e2a36',
  },
  drawerContent: {
    paddingTop: 0,
    width: '100%',
    backgroundColor: 'red',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 0,
    backgroundColor: 'transparent',
  },
  iconContainer: {
    width: 60,
    height: 55,
    backgroundColor: Colors.COMMON_DRAWER_ICON_CONTAINER_BG_COLOR,
    alignItems: 'center',
    justifyContent: 'center',
  },
  menuIcon: {
    width: 32,
    height: 32,
    tintColor: '#e74c3c',
  },
  menuText: {
    color: '#a7b6c2',
    fontSize: Sizes.MEDIUM,
    fontWeight: '700',
    fontFamily: Fonts.ROBO_REGULAR,
    marginLeft: 16,
  },
  logoutButtonContainer: {
    paddingHorizontal: 55,
    marginTop: 16,
    marginBottom: 60,
  },
  logoutButton: {
    borderRadius: 30,
    paddingVertical: 12,
    width: '100%',
    marginTop: 22,
  },
  logoutText: {
    color: '#fff',
    fontSize: Sizes.XLARGE,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '700',
  },
  versionContainer: {
    position: 'absolute',
    bottom: 4,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    borderTopWidth: 1,
    borderTopColor: Colors.SECONDARY,
    opacity: 0.7,
    paddingVertical: 14,
    backgroundColor: '#1e2a36',
  },
  versionText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontSize: Sizes.SMALL,
    textAlign: 'center',
    fontWeight: '800',
  },
  header: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.COMMON_DRAWER_HEADER_TITLE_BG_COLOR,
    paddingVertical: 12,
  },
  logo: {
    width: 160,
    height: 40,
  },
  menuDivider: {
    height: 1,
    backgroundColor: 'rgba(255,255,255,0.08)',
    marginLeft: 0,
    marginRight: 0,
  },
});

export default CustomDrawerContent;