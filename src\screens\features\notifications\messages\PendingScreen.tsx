import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, Image } from 'react-native';
import ScreenLayout from '../../../../components/layout/ScreenLayout';
import TitleSection from '../../../../components/common/TitleSection';
import CustomTab from '../../../../components/common/CustomTabs';
import MessageSection from './MessageSection';
import EmptyPending from './EmptyPending';
import { AppStrings } from '../../../../utils/constants/AppStrings';
import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';
import { AppCommonIcons } from '../../../../utils/constants/AppStrings';
import { messageSection, messageTabData, pendingTasksData, taskStatusData } from '../../../../utils/templates/TemplateConfig';
import { wp } from '../../../../utils/ResponsiveParams';

type TabType = keyof typeof messageTabData;
type TaskStatus = typeof taskStatusData[keyof typeof taskStatusData];

interface PendingTask {
  id: string;
  date: string;
  month: string;
  title: string;
  subtitle: string;
  status: TaskStatus;
  icon: object;
}

const PendingScreen = () => {
  const [activeTab, setActiveTab] = useState<TabType>('PENDING' as TabType);

  // Get pending tasks data from template config
  const pendingTasks: PendingTask[] = pendingTasksData.map(task => ({
    ...task,
    status: task.status,
    icon: AppCommonIcons.MCX_SETTINGS_ICON,
  }));

  const handleTabPress = (tab: TabType) => {
    setActiveTab(tab);
  };
  const handleDeleteMessage = (messageId: string) => {
    // Logic to delete a message
    console.log('Delete message:', messageId);
  };

  const ItemSeparator = () => <View style={styles.itemSeparator} />;
  const renderTabs = () => {
    return (
      <View style={styles.tabsContainer}>
        {Object.entries(messageTabData).map(([key, label]) => (
          <CustomTab
            key={key}
            label={label}
            active={activeTab === key}
            onPress={() => handleTabPress(key as TabType)}
          />
        ))}
      </View>
    );
  };

  const renderSubTabs = () => {
    return (
       <MessageSection
            sections={[messageSection.TYPES, messageSection.DETAILS]}
            onDeletePress={() => handleDeleteMessage}
          />
    );
  };

  const renderPendingTask = ({ item }: { item: PendingTask }) => {
    return (
       <View style={styles.taskContainer}>
      {/* Left image block */}
      <View style={styles.leftImageBlock}>
        <Image source={AppCommonIcons.MCX_SETTINGS_ICON} style={styles.leftImage} />
      </View>

      {/* Date block */}
      <View style={styles.dateCard}>
        <Text style={styles.dateNumber}>{item.date}</Text>
        <Text style={styles.dateMonth}>{item.month}</Text>
      </View>

      {/* Right content block */}
      <View style={styles.taskContent}>
        <View style={styles.taskHeader}>
          <Image source={item.icon} style={styles.taskIcon} />
          <View style={styles.taskInfo}>
            <Text style={styles.taskTitle}>{item.title}</Text>
            <Text style={styles.taskSubtitle}>{item.subtitle}</Text>
          </View>
        </View>

        <View style={[
          styles.statusContainer,
          item.status === taskStatusData.PENDING ? styles.pendingStatus : styles.declinedStatus,
        ]}>
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>
    </View>
    );
  };

  const renderContent = () => {
    if (pendingTasks.length === 0) {
      return <EmptyPending message={AppStrings.MCX_PENDING_MESSAGES_TEXT} />;
    }

    return (
      <FlatList
        data={pendingTasks}
        keyExtractor={(item) => item.id}
        renderItem={renderPendingTask}
        style={styles.tasksList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.tasksListContent}
      />
    );
  };

  return (
    <ScreenLayout
      useScrollView={true}
      useImageBackground={true}
      centerContent={false}
    >
      <TitleSection
        title={AppStrings.MCX_MESSAGES_TITLE}
        bgColor={Colors.PRIMARY}
        textColor={Colors.COMMON_WHITE_SHADE}
        style={styles.titleSection}
      />

      <View style={styles.container}>
        {renderTabs()}

        <View style={styles.contentContainer}>
          {renderSubTabs()}
          {renderContent()}
        </View>
      </View>
    </ScreenLayout>
  );
};
const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
  },
  titleSection: {
    marginBottom: 8,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.COMMON_TAB_SECTION_BG_COLOR,
    justifyContent: 'space-around',
    marginHorizontal: 12,
    marginTop: 20,
  },
  contentContainer: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 12,
    borderBottomEndRadius: 2,
    borderBottomStartRadius:2,
  },
  tasksList: {
    flexGrow: 0,
  },
  tasksListContent: {
    paddingTop: 10,
    paddingBottom: 40,
  },
  taskContainer: {
    flexDirection: 'row',
    backgroundColor: 'red',
    borderRadius: 2,
    marginHorizontal: 12,
    marginBottom: 8,
    borderColor: Colors.COMMON_GREY_SHADOW_LIGHT_COLOR,
    borderWidth: 2,
  },

  dateCard: {
  width: 60,
  backgroundColor: Colors.PRIMARY,
  justifyContent: 'center',
  alignItems: 'center',
},
  dateNumber: {
    fontSize: Sizes.XXXLARGE,
    fontWeight: 'bold',
    color: Colors.COMMON_WHITE_SHADE,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  dateMonth: {
    fontSize: Sizes.SMALL,
    color: Colors.COMMON_WHITE_SHADE,
    fontFamily: Fonts.ROBO_REGULAR,
    opacity:0.5,
  },
  taskContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  taskHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(4),
  },
  taskIcon: {
    width: 42,
    height: 42,
    opacity:0.5,
    marginVertical: 16,
    tintColor: Colors.COMMON_GREY_SHADE_DARK,
  },
  taskInfo: {
    flex: 1,
    borderLeftWidth:1,
    borderColor: Colors.COMMON_GREY_SHADOW_LIGHT_COLOR,
    marginHorizontal: wp(4),
    paddingVertical: 16,
    paddingHorizontal:6,
  },
  taskTitle: {
    fontSize: Sizes.MEDIUM,
    fontWeight: 'bold',
    color: Colors.COMMON_BlACK_SHADE,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  taskSubtitle: {
    fontSize: Sizes.SMALL,
    color: Colors.PRIMARY,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight:'600',
    flex: 1,
  },
  statusContainer: {
    alignSelf: 'stretch',
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  pendingStatus: {
    backgroundColor: '#E53E3E',
  },
  declinedStatus: {
    backgroundColor: '#FF8C00',
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: Sizes.SMALL,
    fontWeight: 'bold',
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
  },
  leftImageBlock: {
  width: 60,
  height: '100%',
  backgroundColor: Colors.PRIMARY_DARK, // or any preferred color
  justifyContent: 'center',
  alignItems: 'center',
  borderTopLeftRadius: 2,
  borderBottomLeftRadius: 2,
},
leftImage: {
  width: 32,
  height: 32,
  opacity: 0.9,
},
});

export default PendingScreen;
