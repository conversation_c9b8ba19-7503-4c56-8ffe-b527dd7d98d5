import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Image } from 'react-native';
import ScreenLayout from '../../../../components/layout/ScreenLayout';
import TitleSection from '../../../../components/common/TitleSection';
import CustomTab from '../../../../components/common/CustomTabs';
import EmptyPending from './EmptyPending';
import { AppStrings } from '../../../../utils/constants/AppStrings';
import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';
import { AppCommonIcons } from '../../../../utils/constants/AppStrings';
import { messageTabData, pendingTasksData } from '../../../../utils/templates/TemplateConfig';

type TabType = 'ALL' | 'SERVICES' | 'SYSTEMS' | 'PENDING';

interface PendingTask {
  id: string;
  date: string;
  month: string;
  title: string;
  subtitle: string;
  status: 'Pending' | 'Declined';
  icon: object;
}

const PendingScreen = () => {
  const [activeTab, setActiveTab] = useState<TabType>('PENDING');

  // Get pending tasks data from template config
  const pendingTasks: PendingTask[] = pendingTasksData.map(task => ({
    ...task,
    status: task.status as 'Pending' | 'Declined',
    icon: AppCommonIcons.MCX_USER_PROFILE_PIC, // Replace with appropriate icon
  }));

  const handleTabPress = (tab: TabType) => {
    setActiveTab(tab);
  };

  const renderTabs = () => {
    return (
      <View style={styles.tabsContainer}>
        {Object.entries(messageTabData).map(([key, label]) => (
          <CustomTab
            key={key}
            label={label}
            active={activeTab === key}
            onPress={() => handleTabPress(key as TabType)}
          />
        ))}
      </View>
    );
  };

  const renderSubTabs = () => {
    return (
      <View style={styles.subTabsContainer}>
        <TouchableOpacity style={[styles.subTab, styles.activeSubTab]}>
          <Text style={[styles.subTabText, styles.activeSubTabText]}>Types</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.subTab}>
          <Text style={styles.subTabText}>Details</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderPendingTask = ({ item }: { item: PendingTask }) => {
    return (
      <View style={styles.taskContainer}>
        <View style={styles.dateCard}>
          <Text style={styles.dateNumber}>{item.date}</Text>
          <Text style={styles.dateMonth}>{item.month}</Text>
        </View>

        <View style={styles.taskContent}>
          <View style={styles.taskHeader}>
            <Image source={item.icon} style={styles.taskIcon} />
            <View style={styles.taskInfo}>
              <Text style={styles.taskTitle} numberOfLines={0}>{item.title}</Text>
              <Text style={styles.taskSubtitle} numberOfLines={0}>{item.subtitle}</Text>
            </View>
          </View>

          <View style={[
            styles.statusContainer,
            item.status === 'Pending' ? styles.pendingStatus : styles.declinedStatus,
          ]}>
            <Text style={styles.statusText}>{item.status}</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderContent = () => {
    if (pendingTasks.length === 0) {
      return <EmptyPending message="No pending tasks available" />;
    }

    return (
      <FlatList
        data={pendingTasks}
        keyExtractor={(item) => item.id}
        renderItem={renderPendingTask}
        style={styles.tasksList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.tasksListContent}
      />
    );
  };

  return (
    <ScreenLayout
      useScrollView={false}
      useImageBackground={true}
      centerContent={false}
    >
      <TitleSection
        title={AppStrings.MCX_MESSAGES_TITLE}
        bgColor={Colors.PRIMARY}
        textColor="#fff"
        style={styles.titleSection}
      />

      <View style={styles.container}>
        {renderTabs()}

        <View style={styles.contentContainer}>
          {renderSubTabs()}
          {renderContent()}
        </View>
      </View>
    </ScreenLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  titleSection: {
    marginBottom: 8,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.COMMON_TAB_SECTION_BG_COLOR,
    justifyContent: 'space-around',
    marginHorizontal: 12,
    marginTop: 16,
  },
  contentContainer: {
    backgroundColor: '#FFFFFF',
    flex: 1,
    marginHorizontal: 12,
    marginTop: 8,
    borderRadius: 2,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  subTabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: Colors.COMMON_GREY_SHADE_LIGHT,
    paddingHorizontal: 16,
  },
  subTab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 24,
  },
  activeSubTab: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.PRIMARY,
  },
  subTabText: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.COMMON_GREY_SHADE_DARK,
  },
  activeSubTabText: {
    color: Colors.PRIMARY,
    fontWeight: 'bold',
  },
  tasksList: {
    flex: 1,
  },
  tasksListContent: {
    padding: 16,
    flexGrow: 1,
  },
  taskContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 2,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  dateCard: {
    backgroundColor: '#8B1538',
    width: 60,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 2,
    borderBottomLeftRadius: 2,
  },
  dateNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontFamily: Fonts.ROBO_REGULAR,
  },
  dateMonth: {
    fontSize: 12,
    color: '#FFFFFF',
    fontFamily: Fonts.ROBO_REGULAR,
  },
  taskContent: {
    flex: 1,
    padding: 12,
    justifyContent: 'space-between',
    minHeight: 80,
  },
  taskHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  taskIcon: {
    width: 24,
    height: 24,
    marginRight: 12,
    tintColor: Colors.COMMON_GREY_SHADE_DARK,
  },
  taskInfo: {
    flex: 1,
  },
  taskTitle: {
    fontSize: Sizes.MEDIUM,
    fontWeight: 'bold',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    flexWrap: 'wrap',
    flex: 1,
  },
  taskSubtitle: {
    fontSize: Sizes.SMALL,
    color: Colors.COMMON_GREY_SHADE_DARK,
    fontFamily: Fonts.ROBO_REGULAR,
    marginTop: 2,
    flexWrap: 'wrap',
    flex: 1,
  },
  statusContainer: {
    alignSelf: 'flex-end',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 2,
    marginTop: 8,
  },
  pendingStatus: {
    backgroundColor: '#FF4444',
  },
  declinedStatus: {
    backgroundColor: '#FF6666',
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: Sizes.SMALL,
    fontWeight: 'bold',
    fontFamily: Fonts.ROBO_REGULAR,
  },
});

export default PendingScreen;
