import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';

import EmptyMessages from './EmptyMessages';
import MessageSection from './MessageSection';
import { messageSection, messageTabData } from '../../../../utils/templates/TemplateConfig';
import CustomTab from '../../../../components/common/CustomTabs';
import ScreenLayout from '../../../../components/layout/ScreenLayout';
import TitleSection from '../../../../components/common/TitleSection';
import { AppStrings, RouteNames } from '../../../../utils/constants/AppStrings';
import { Colors } from '../../../../utils/constants/Theme';

type TabType = 'ALL' | 'SERVICES' | 'SYSTEMS' | 'PENDING';

const MessagesScreen = () => {
  const navigation = useNavigation();
  const [activeTab, setActiveTab] = useState<TabType>('ALL');
  const [messages, setMessages] = useState([]);

  const handleTabPress = (tab: TabType) => {
    setActiveTab(tab);
    if (tab === 'PENDING') {
      navigation.navigate(RouteNames.MCX_NAV_PENDING as never);
    }
     if (tab === 'ALL') {
      navigation.navigate(RouteNames.MCX_NAV_PENDING as never);
    }
    // Here you would typically fetch messages based on the selected tab
  };

  const handleDeleteMessage = (messageId: string) => {
    // Logic to delete a message
    console.log('Delete message:', messageId);
  };

  const renderTabs = () => {
    return (
      <View style={styles.tabsContainer}>
        {Object.entries(messageTabData).map(([key, label]) => (
          <CustomTab
            key={key}
            label={label}
            active={activeTab === key}
            onPress={() => handleTabPress(key as TabType)}
          />
        ))}
      </View>
    );
  };

  return (
      <ScreenLayout
        useScrollView={false}
        useImageBackground={true}
        centerContent={false}
      >
        {/* Title Section */}
        <TitleSection
          title={AppStrings.MCX_MESSAGES_TITLE}
          bgColor={Colors.PRIMARY}
          textColor="#fff"
          style={styles.titleSection}
        />
        <View style={styles.container}>
        {renderTabs()}

        <View style={styles.contentContainer}>
          <MessageSection
            sections={[messageSection.TYPES, messageSection.DETAILS]}
            onDeletePress={() => handleDeleteMessage}
          />

          <ScrollView>
            {messages.length > 0 ? (
              messages.map((message, index) => (
                <View key={index}>
                  {/* Message items would go here */}
                </View>
              ))
            ) : (
              <EmptyMessages />
            )}
          </ScrollView>
        </View>
         </View>
      </ScreenLayout>

  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
   titleSection: {
    marginBottom: 8,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.COMMON_TAB_SECTION_BG_COLOR,
    justifyContent:'space-around',
    marginHorizontal:12,
     marginTop:16,
  },
  contentContainer: {
  backgroundColor: '#FFFFFF',
  marginHorizontal: 12,
  paddingBottom: 20, // optional padding if needed
},
});

export default MessagesScreen;
