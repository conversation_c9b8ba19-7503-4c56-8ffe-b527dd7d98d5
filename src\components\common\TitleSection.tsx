import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { CommonUIParams, Fonts, Sizes } from '../../utils/constants/Theme';

interface TitleSectionProps {
  title: string;
  bgColor: string;
  textColor: string;
  style?: object;
  textStyle?: object;
}

const TitleSection: React.FC<TitleSectionProps> = ({ title, bgColor, textColor, style, textStyle }) => (
  <View style={[styles.TitleSection, { backgroundColor: bgColor }, style]}>
    <Text style={[styles.TitleSectionText, { color: textColor }, textStyle]}>{title}</Text>
  </View>
);

const styles = StyleSheet.create({
  TitleSection: {
    width: '100%',
    height: CommonUIParams.CUSTOM_SECTION_TITLE_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  TitleSectionText: {
    fontWeight: '600',
    fontSize: Sizes.XXLARGE,
    fontFamily: Fonts.ROBO_REGULAR,
  },
});

export default TitleSection;