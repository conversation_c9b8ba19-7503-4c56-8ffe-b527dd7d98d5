import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import TitleSection from '../../../components/common/TitleSection';
import CustomTab from '../../../components/common/CustomTabs';
import { registrationTabData } from '../../../utils/templates/TemplateConfig';
import { AppStrings } from '../../../utils/constants/AppStrings';
import { Colors } from '../../../utils/constants/Theme';
import RegistrationStep1 from './components/RegistrationStep1';
import RegistrationStep2 from './components/RegistrationStep2';
import LoadVinScreen from './components/LoadVinScreen';
import RegistrationStep4 from './components/RegistrationStep4';
// Import tab components.

type TabType = keyof typeof registrationTabData;

const AccountRegistration = () => {
  const [activeTab, setActiveTab] = useState<TabType>('3');

  const handleTabPress = (tab: TabType) => {
    setActiveTab(tab);
  };

  const renderTabs = () => {
    return (
      <View style={styles.tabsContainer}>
        {Object.entries(registrationTabData).map(([key, label]) => (
          <CustomTab
            key={key}
            label={label}
            active={activeTab === key}
            onPress={() => handleTabPress(key as TabType)}
          />
        ))}
      </View>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case '1':
        return <RegistrationStep1 />;
      case '2':
        return <RegistrationStep2 />;
      case '3':
        return <LoadVinScreen />;
      case '4':
        return <RegistrationStep4 />;
      default:
        return <LoadVinScreen />;
    }
  };

  return (
    <ScreenLayout
      useScrollView={false}
      useImageBackground={true}
      centerContent={false}
    >
      {/* Title Section */}
      <TitleSection
        title={AppStrings.MCX_ACCOUNT_REGISTRATION_TITLE}
        bgColor={Colors.PRIMARY}
        textColor="#fff"
        style={styles.titleSection}
      />
      <View style={styles.container}>
        {renderTabs()}
        <View style={styles.contentContainer}>
          {renderTabContent()}
        </View>
      </View>
    </ScreenLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  titleSection: {
    marginBottom: 8,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.COMMON_TAB_SECTION_BG_COLOR,
    justifyContent: 'space-around',
    marginHorizontal: 12,
    marginTop: 16,
  },
  contentContainer: {
    backgroundColor: 'transparent',
    flex: 1,
  },
});

export default AccountRegistration;
